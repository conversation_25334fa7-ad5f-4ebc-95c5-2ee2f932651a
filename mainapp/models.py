import os
import json
import hashlib
import base64
import secrets
from typing import List, Any, Dict

import tldextract
from cryptography.fernet import <PERSON><PERSON><PERSON>

from django.db import models
from django.utils import timezone
from django.conf import settings
from rest_framework.request import Request
from django.contrib.auth.hashers import make_password
from django.contrib.postgres.fields import ArrayField
from django.contrib.auth.models import BaseUserManager
from django.contrib.auth.models import AbstractBaseUser
from django.contrib.auth.models import User

# ===================================================================================
# -------------------------------- UTILITY FUNCTIONS --------------------------------
# ===================================================================================

def generate_plugin_uid() -> str:
    """
    Generates a 36 character unique plugin id
    """
    return "-".join([secrets.token_hex(4) for _ in range(4)])


def keywords_trend_default() -> dict:
    """
    Used to set default immutable dictionary value for Keywords table 'country_data' field
    """
    return {'trends': []}


def get_logo_upload_to(_instance, filename) -> str:
    return f"{os.environ['CLOUDFLARE_R2_ENV']}/logo/{filename}"


def get_article_image_upload_to(_instance, filename) -> str:
    return f"{os.environ['CLOUDFLARE_R2_ENV']}/article-images/{filename}"


# ===================================================================================
# -------------------------------- CUSTOM EXCEPTIONS --------------------------------
# ===================================================================================

class FeaturedImageModelError(Exception):
    """
    For all errors related to saving Website model
    """
    def __init__(self, article_uid: str, err_msg: str):
        super(FeaturedImageModelError, self).__init__(f"Featured Image model error for article {article_uid} - {err_msg}")


class KubernetesLogError(Exception):
    """
    For all errors related to saving KubernetesJobLogs model
    """
    def __init__(self, err_msg):
        super(KubernetesLogError, self).__init__(err_msg)

# ===============================================================================
# ------------------------------------ USERS ------------------------------------
# ===============================================================================

class CustomUserManager(BaseUserManager):
    def create_user(self, username: str,
                    email: str,
                    password: str,
                    # team: Team,
                    **extra_fields):
        email = self.normalize_email(email)
        # self.model calls User class with provided arguments
        # user = self.model(username=username, email=email, team=team, **extra_fields)
        user = self.model(username=username, email=email, **extra_fields)
        user.password = make_password(password)
        # user.current_active_team = team
        user.save()
        return user


class User(AbstractBaseUser):
    """
    Custom user model. Inherits AbstractBaseUser model.

    password and last_login are inherited from base class
    """
    PROTOCOL_TYPES = [
        ('http', 'http'),
        ('https', 'https'),
    ]

    KEYWORD_STRATEGIES = [
        ('volume', "Volume Based"),
        ('cpc', "CPC Based"),
        ('competition', "Competition Based"),
    ]

    objects = CustomUserManager()

    admin = models.BooleanField(default=False)  # Use to mark account as Abun 'admin'

    email = models.EmailField(max_length=254, unique=True, db_index=True)
    verified = models.BooleanField(default=False)
    username = models.CharField(max_length=150, blank=False, null=False)
    user_timezone = models.CharField(max_length=100, default='UTC')
    date_joined = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    last_login = models.DateTimeField(default=timezone.now)
    user_tz = models.CharField(max_length=300, default='UTC')
    country = models.CharField(max_length=300, default="United States")
    signup_using_google = models.BooleanField(default=False)
    last_login_using_google = models.BooleanField(default=False)
    password_setup_required = models.BooleanField(default=False)

    # ------ for stripe payment ------
    stripe_customer_id = models.CharField(max_length=300, null=True, blank=False, default=None)
    stripe_subscription_id = models.CharField(max_length=300, blank=False, null=True, default=None)
    stripe_product_id = models.CharField(max_length=300, blank=False, null=True, default=None)
    stripe_pricing_id = models.CharField(max_length=300, blank=False, null=True, default=None)
    payment_pending = models.BooleanField(default=False)
    stripe_active_checkout_session_id = models.CharField(max_length=500, blank=False, null=True, default=None)
    next_renewal_date = models.DateTimeField(null=True, default=None)

    # ------ for reset password ------
    reset_password_uid = models.CharField(max_length=64, null=True, blank=False, default=None)
    reset_password_expiry = models.DateTimeField(null=True, default=None)

    # ------ preferences/settings ------
    send_notification_emails = models.BooleanField(default=True)

    # ------ for email cronjobs ------
    feedback_email_sent = models.BooleanField(default=False)
    founders_email_sent = models.BooleanField(default=False)
    article_email_sent = models.BooleanField(default=False)
    no_plan_selection_email_sent = models.BooleanField(default=False)

    # ------ for survey ------
    survey_completed = models.BooleanField(default=False)

    # ------ for appsumo license ------
    appsumo_licenses = models.ManyToManyField("AppSumoLicense")

    # ------ title stats ------
    titles_generated = models.IntegerField(default=0)
    total_titles_generated = models.IntegerField(default=0)

    # ------ article stats ------
    articles_generated = models.IntegerField(default=0)
    total_articles_generated = models.IntegerField(default=0)

    # ------ keyword stats ------
    keywords_generated = models.IntegerField(default=0)
    total_keywords_generated = models.IntegerField(default=0)

    # ------ blog finder email stats ------
    blog_emails_found = models.IntegerField(default=0)
    total_blog_emails_found = models.IntegerField(default=0)

    # ------ glossary stats ------
    glossary_contents_generated = models.IntegerField(default=0)
    glossary_topic_generated = models.IntegerField(default=0)

    # ------ guest post finder stats ------
    guest_post_finder_queries_generated = models.IntegerField(default=0)

    # ------ reddit post finder stats ------
    reddit_post_finder_queries_generated = models.IntegerField(default=0)

    # ------ ai calculator stats ------
    ai_calculators_generated = models.IntegerField(default=0)
    total_ai_calculators_generated = models.IntegerField(default=0)

    # ------ AI stats page ------
    stats_pages_generated = models.IntegerField(default=0)

    # ------ AI comparison page ------
    comparison_pages_generated = models.IntegerField(default=0)

    # ------ automation project stats ------
    automation_projects_generated = models.IntegerField(default=0)
    total_automation_projects_generated = models.IntegerField(default=0)

    # ------ content calendar stats ------
    content_calendar_generated = models.IntegerField(default=0)

    # ------ search console insights stats ------
    search_console_insights_generated = models.IntegerField(default=0)

    # ------ fast indexing stats ------
    fast_indexing_generated = models.IntegerField(default=0)

    # TODO: when the website referenced in current_active_website is deleted,
    #        things will be handled by pre_save signal on Website model
    current_active_website = models.ForeignKey('Website',
                                               null=True,
                                               on_delete=models.DO_NOTHING,
                                               related_name='active_website')
    
    plugin_uid = models.CharField(max_length=40, default=generate_plugin_uid)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']  # only for createsuperuser

    latest_ip_info = models.JSONField(default=dict)

    @staticmethod
    def get_domain(site_url: str | None) -> str | None:
        """
        Returns domain from the url
        :param site_url: Connected site url
        """
        try:
            extract = tldextract.extract(site_url)

            if extract.subdomain:
                return f"{extract.subdomain}.{extract.domain}.{extract.suffix}"
            else:
                return extract.registered_domain

        except AttributeError:
            return None

    @property
    def all_integrations(self) -> List:
        """
        Returns a list of connected integrations for article posting
        """
        integrations = []
        all_integrations_models = [WordpressIntegration, WebflowIntegration,
                                   WixIntegration, ShopifyIntegration, GhostIntegration, GHLIntegration]

        for integration in all_integrations_models:
            value: Any = integration.objects.filter(website=self.current_active_website)

            for obj in value.all():
                integration_name = obj.__class__.__name__.lower().replace("integration", "")

                if isinstance(obj, ShopifyIntegration):
                    integrations.append(f"{integration_name} - {obj.shop_url}")
                elif isinstance(obj, GHLIntegration):
                    integrations.append(f"{integration_name} - {self.get_domain(obj.ghl_domain)}")
                elif hasattr(obj, "site_url"):
                    if obj.site_url:
                        integrations.append(f"{integration_name} - {self.get_domain(obj.site_url)}")
                    else:
                        integrations.append(f"{integration_name} - {obj.site_id}")
                else:
                    integrations.append(f"{integration_name} - {obj.site_id}")

        return integrations

    @property
    def all_integrations_with_unique_id(self) -> List[Dict]:
        """
        Returns a list of connected integrations with a unique integration ID
        """
        all_integrations_models = [WordpressIntegration, WebflowIntegration,
                                   WixIntegration, ShopifyIntegration, GhostIntegration, GHLIntegration]

        integrations = []
        unique_id_mapping = {
            "WordpressIntegration": "site_url",
            "WebflowIntegration": "collection_id",
            "WixIntegration": "site_id",
            "ShopifyIntegration": "shop_url",
            "GhostIntegration": "site_url",
            "GHLIntegration": "ghl_domain",
        }

        for integration in all_integrations_models:
            value: Any = integration.objects.filter(website=self.current_active_website)

            for obj in value.all():
                class_name = obj.__class__.__name__.lower().replace("integration", "")

                if hasattr(obj, "site_url"):
                    if obj.site_url:
                        integration_name = f"{class_name} - {self.get_domain(obj.site_url)}"
                    else:                        
                        integration_name = f"{class_name} - {obj.site_id}"
                elif hasattr(obj, "ghl_domain"):
                    integration_name = f"{class_name} - {self.get_domain(obj.ghl_domain)}"
                elif hasattr(obj, "shop_url"):
                    integration_name = f"{class_name} - {obj.shop_url}"

                integrations.append({
                    "integrationName": integration_name,
                    "integrationUniqueID": getattr(obj, unique_id_mapping[obj.__class__.__name__])
                })

        return integrations

    @property
    def keywords(self) -> models.QuerySet:
        """
        Returns current active website keywords
        """
        return Keyword.objects.filter(website=self.current_active_website)

    @property
    def keyword_projects(self) -> models.QuerySet:
        """
        Returns current active website keyword projects
        """
        return KeywordProject.objects.filter(website=self.current_active_website)

    @property
    def automation_projects(self) -> models.QuerySet:
        """
        Returns current active website automation projects
        """
        return AutomationProject.objects.filter(website=self.current_active_website)

    @property
    def articles(self) -> models.QuerySet:
        """
        Returns current active website articles
        """
        return Article.objects.all_queryset().filter(website=self.current_active_website)

    @property
    def programmatic_seo_titles(self) -> models.QuerySet:
        """
        Returns current active website programmatic seo titles
        """
        return ProgrammaticSeoTitle.objects.filter(website=self.current_active_website)

    @property
    def glossary_topics(self) -> models.QuerySet:
        """
        Returns current active website glossary words
        """
        return GlossaryTopic.objects.filter(website=self.current_active_website)

    @property
    def glossary_content(self) -> models.QuerySet:
        """
        Returns current active website articles
        """
        return GlossaryContent.objects.filter(website=self.current_active_website)

    @property
    def wordpress_integrations(self) -> models.QuerySet:
        """
        Returns current active website wordpres sites
        """
        return WordpressIntegration.objects.filter(website=self.current_active_website)

    @property
    def wix_integrations(self) -> models.QuerySet:
        """
        Returns current active website wordpress sites
        """
        return WixIntegration.objects.filter(website=self.current_active_website)

    @property
    def webflow_integrations(self) -> models.QuerySet:
        """
        Returns current active website webflow sites
        """
        return WebflowIntegration.objects.filter(website=self.current_active_website)

    @property
    def shopify_integrations(self) -> models.QuerySet:
        """
        Returns current active website shopify sites
        """
        return ShopifyIntegration.objects.filter(website=self.current_active_website)

    @property
    def ghost_integrations(self) -> models.QuerySet:
        """
        Returns current active website ghost sites
        """
        return GhostIntegration.objects.filter(website=self.current_active_website)

    @property
    def ghl_integrations(self) -> models.QuerySet:
        """
        Returns current active website GHL sites
        """
        return GHLIntegration.objects.filter(website=self.current_active_website)
    
    @property
    def gsc_integrations(self) -> models.QuerySet:
        """
        Returns current active website gsc sites
        """
        return GoogleIntegration.objects.filter(website=self.current_active_website)

    @property
    def keyword_strategy(self):
        """
        Returns current active website keyword strategy
        """
        if not self.current_active_website:
            return "cpc"
        return self.current_active_website.keyword_strategy

    @keyword_strategy.setter
    def keyword_strategy(self, value: str):
        """
        Saves current active website keyword strategy
        """
        self.current_active_website.keyword_strategy = value
        self.current_active_website.save()

    @property
    def image_source(self):
        """
        Returns current active website image source
        """
        if not self.current_active_website:
            return "no_image"
        return self.current_active_website.image_source

    @image_source.setter
    def image_source(self, value: str):
        """
        Saves current active website image source
        """
        self.current_active_website.image_source = value
        self.current_active_website.save()

    @property
    def feature_image_template_id(self):
        """
        Returns current active website feature image template ID
        """
        if not self.current_active_website:
            return ""
        return self.current_active_website.feature_image_template_id

    @feature_image_template_id.setter
    def feature_image_template_id(self, value: str):
        """
        Saves current active website feature image template ID
        """
        if self.current_active_website:
            self.current_active_website.feature_image_template_id = value
            self.current_active_website.save()

    @property
    def feature_image_required(self):
        """
        Returns current active website feature image template required
        """
        if not self.current_active_website:
            return True
        return self.current_active_website.feature_image_required

    @feature_image_required.setter
    def feature_image_required(self, value: bool):
        """
        Saves current active website feature image template required
        """
        self.current_active_website.feature_image_required = value
        self.current_active_website.save()

    @property
    def generate_bannerbear_featured_image(self):
        """
        Returns current active website bannerbear featured image
        """
        if not self.current_active_website:
            return True
        return self.current_active_website.generate_bannerbear_featured_image

    @generate_bannerbear_featured_image.setter
    def generate_bannerbear_featured_image(self, value: bool):
        """
        Saves current active website bannerbear featured image
        """
        self.current_active_website.generate_bannerbear_featured_image = value
        self.current_active_website.save()

    @property
    def article_tone_of_voice(self):
        """
        Returns current active website article tone of voice
        """
        if not self.current_active_website:
            return "exciting"
        return self.current_active_website.article_tone_of_voice

    @article_tone_of_voice.setter
    def article_tone_of_voice(self, value: str):
        """
        Saves current active website article tone of voice
        """
        self.current_active_website.article_tone_of_voice = value
        self.current_active_website.save()
        
    @property
    def active_integration(self):
        """
        Returns current active website active integration
        """
        if not self.current_active_website:
            return ""
        return self.current_active_website.active_integration

    @active_integration.setter
    def active_integration(self, value: str):
        """
        Saves current active website active integration
        """
        self.current_active_website.active_integration = value
        self.current_active_website.save()

    @property
    def external_backlinks_preference(self):
        """
        Returns current active website backlinks preference
        """
        if not self.current_active_website:
            return "no-follow"
        return self.current_active_website.external_backlinks_preference

    @external_backlinks_preference.setter
    def external_backlinks_preference(self, value: str):
        """
        Saves current active website backlinks preference
        """
        self.current_active_website.external_backlinks_preference = value
        self.current_active_website.save()

    @property
    def max_internal_backlinks(self):
        """
        Returns current active website max internal backlinks
        """
        if not self.current_active_website:
            return 5
        return self.current_active_website.max_internal_backlinks

    @max_internal_backlinks.setter
    def max_internal_backlinks(self, value: int):
        """
        Saves current active website max internal backlinks
        """
        self.current_active_website.max_internal_backlinks = value
        self.current_active_website.save()

    @property
    def max_external_backlinks(self):
        """
        Returns current active website extenal backlinks
        """
        if not self.current_active_website:
            return 2
        return self.current_active_website.max_external_backlinks

    @max_external_backlinks.setter
    def max_external_backlinks(self, value: int):
        """
        Saves current active website extenal backlinks
        """
        self.current_active_website.max_external_backlinks = value
        self.current_active_website.save()

    @property
    def max_internal_glossary_backlinks(self):
        """
        Returns current active website internal glossary backlinks
        """
        if not self.current_active_website:
            return 5
        return self.current_active_website.max_internal_glossary_backlinks

    @max_internal_glossary_backlinks.setter
    def max_internal_glossary_backlinks(self, value: int):
        """
        Saves current active website internal glossary  backlinks
        """
        self.current_active_website.max_internal_glossary_backlinks = value
        self.current_active_website.save()

    @property
    def internal_glossary_backlinks_preference(self):
        """
        Returns current active website internal glossary backlinks preference
        """
        if not self.current_active_website:
            return "off"
        return self.current_active_website.max_internal_glossary_backlinks

    @internal_glossary_backlinks_preference.setter
    def internal_glossary_backlinks_preference(self, value: int):
        """
        Saves current active website internal glossary backlinks preference
        """
        self.current_active_website.internal_glossary_backlinks_preference = value
        self.current_active_website.save()

    @property
    def ai_generated_image_style(self):
        """
        Returns current active website image style
        """
        if not self.current_active_website:
            return "illustration"
        return self.current_active_website.ai_generated_image_style

    @ai_generated_image_style.setter
    def ai_generated_image_style(self, value: str):
        """
        Saves current active website image style
        """
        self.current_active_website.ai_generated_image_style = value
        self.current_active_website.save()

    @property
    def images_file_format(self):
        """
        Returns current active website file format
        """
        if not self.current_active_website:
            return "png"
        return self.current_active_website.images_file_format

    @images_file_format.setter
    def images_file_format(self, value: str):
        """
        Saves current active website file format
        """
        self.current_active_website.images_file_format = value
        self.current_active_website.save()

    @property
    def article_language_preference(self):
        """
        Returns current active website article language preference
        """
        if not self.current_active_website:
            return "english"
        return self.current_active_website.article_language_preference

    @article_language_preference.setter
    def article_language_preference(self, value: str):
        """
        Saves current active website article language preference
        """
        self.current_active_website.article_language_preference = value
        self.current_active_website.save()

    @property
    def show_logo_on_featured_image(self):
        """
        Returns current active website show logo on featured_image
        """
        if not self.current_active_website:
            return False
        return self.current_active_website.show_logo_on_featured_image

    @show_logo_on_featured_image.setter
    def show_logo_on_featured_image(self, value: str):
        """
        Saves current active website show logo on featured_image
        """
        self.current_active_website.show_logo_on_featured_image = value
        self.current_active_website.save()
        
    @property
    def article_context(self):
        """
        Returns current active website article context
        """
        if not self.current_active_website:
            return ""
        return self.current_active_website.article_context

    @article_context.setter
    def article_context(self, value: str):
        """
        Saves current active website article language preference
        """
        self.current_active_website.article_context = value
        self.current_active_website.save()

    @property
    def tone_of_article(self):
        """
        Returns current active website article context
        """
        if not self.current_active_website:
            return ""
        return self.current_active_website.tone_of_article

    @tone_of_article.setter
    def tone_of_article(self, value: str):
        """
        Saves current active website article language preference
        """
        self.current_active_website.tone_of_article = value
        self.current_active_website.save()

    @property
    def scale_of_tone(self):
        """
        Returns current active website article context
        """
        if not self.current_active_website:
            return ""
        return self.current_active_website.scale_of_tone

    @scale_of_tone.setter
    def scale_of_tone(self, value: str):
        """
        Saves current active website article language preference
        """
        self.current_active_website.scale_of_tone = value
        self.current_active_website.save()
        
    @property
    def toggle_toc(self):
        """
        Returns current active website toggle toc
        """
        if not self.current_active_website:
            return False
        return self.current_active_website.toggle_toc

    @toggle_toc.setter
    def toggle_toc(self, value: bool):
        """
        Saves current active website toggle toc
        """
        self.current_active_website.toggle_toc = value
        self.current_active_website.save()

    @property
    def toggle_table(self):
        """
        Returns current active website toggle table
        """
        if not self.current_active_website:
            return False
        return self.current_active_website.toggle_table

    @toggle_table.setter
    def toggle_table(self, value: bool):
        """
        Saves current active website toggle table
        """
        self.current_active_website.toggle_table = value
        self.current_active_website.save()
    
    @property
    def toggle_meta_description(self):
        """
        Returns current active website toggle meta description
        """
        if not self.current_active_website:
            return False        
        return self.current_active_website.toggle_meta_description

    @toggle_meta_description.setter
    def toggle_meta_description(self, value: bool):
        """
        Saves current active website toggle meta description
        """
        self.current_active_website.toggle_meta_description = value
        self.current_active_website.save()
    
    @property
    def toggle_faq(self):
        """
        Returns current active website toggle faq
        """
        if not self.current_active_website:
            return False        
        return self.current_active_website.toggle_faq

    @toggle_faq.setter
    def toggle_faq(self, value: bool):
        """
        Saves current active website toggle faq
        """
        self.current_active_website.toggle_faq = value
        self.current_active_website.save()
    
    @property
    def toggle_tldr(self):
        """
        Returns current active website toggle tldr
        """
        if not self.current_active_website:
            return False        
        return self.current_active_website.toggle_tldr

    @toggle_tldr.setter
    def toggle_tldr(self, value: bool):
        """
        Saves current active website toggle tldr
        """
        self.current_active_website.toggle_tldr = value
        self.current_active_website.save()
    
    @property
    def toggle_bullet_points(self):
        """
        Returns current active website toggle bullet points
        """
        if not self.current_active_website:
            return False        
        return self.current_active_website.toggle_bullet_points

    @toggle_bullet_points.setter
    def toggle_bullet_points(self, value: bool):
        """
        Saves current active website toggle bullet points
        """
        self.current_active_website.toggle_bullet_points = value
        self.current_active_website.save()
        
    @property
    def auto_indexing(self):
        """
        Returns current active website auto indexing
        """
        if not self.current_active_website:
            return False        
        return self.current_active_website.auto_indexing

    @auto_indexing.setter
    def auto_indexing(self, value: bool):
        """
        Saves current active website auto indexing
        """
        self.current_active_website.auto_indexing = value
        self.current_active_website.save()
        
    @property
    def last_auto_indexed_on(self):
        """
        Returns current active website last auto indexed on
        """
        if not self.current_active_website:
            return None        
        return self.current_active_website.last_auto_indexed_on

    @last_auto_indexed_on.setter
    def last_auto_indexed_on(self, value: bool):
        """
        Saves current active website last auto indexed on
        """
        self.current_active_website.last_auto_indexed_on = value
        self.current_active_website.save()
        
    @property
    def feature_image_template_label(self):
        if not self.current_active_website:
            return ""
        return self.current_active_website.feature_image_template_label

    @property
    def has_ltd_plans(self):
        return self.active_ltd_plans.exists()

    @property
    def active_ltd_plans(self):
        return self.appsumo_licenses.filter(license_status="active")

    def __str__(self):
        return self.email

    @property
    def blog_finder(self) -> models.QuerySet:
        """
        Returns current active website blog finder
        """
        return BlogFinder.objects.filter(website=self.current_active_website)

    @property
    def blog_finder_project(self) -> models.QuerySet:
        """
        Returns current active website blog finder
        """
        return BlogFinderProject.objects.filter(website=self.current_active_website)

    @property
    def ai_calculators(self) -> models.QuerySet:
        """
        Returns current active website AI calculators
        """
        return AICalculator.objects.filter(website=self.current_active_website)

    @property
    def encrypted_id(self):
        """
        Returns encrypted user id
        """
        # Encrypt the website ID using the same method as AICalculator
        key = base64.urlsafe_b64encode(hashlib.sha256(settings.SECRET_KEY.encode()).digest())

        # Create a Fernet instance with the key
        f = Fernet(key)

        # Encrypt the website ID
        encrypted_id = f.encrypt(str(self.email).encode()).decode()
        return encrypted_id

    def get_tools_integration_script(self, request: Request):
        """
        Returns the HTML script tag for tools embedding.
        """
        # Encrypt the website ID using the same method as AICalculator
        key = base64.urlsafe_b64encode(hashlib.sha256(settings.SECRET_KEY.encode()).digest())

        # Create a Fernet instance with the key
        f = Fernet(key)

        # Encrypt the website ID
        encrypted_id = f.encrypt(str(self.email).encode()).decode()

        server_url: str = request.build_absolute_uri('/')[:-1].replace('http://', 'https://')
        path: str = f"{server_url}/api/frontend/get-tools-loading-script/"
        return f"<script async src=\"{path}\" data-user-id=\"{encrypted_id}\" crossorigin=\"anonymous\"></script>"
    
    @property
    def ai_stats_pages(self):
        """
        Returns current active website AI stats pages for this user
        """
        if self.current_active_website:
            return AIStatsPage.objects.filter(website=self.current_active_website)
        return AIStatsPage.objects.none()
    
    @property
    def ai_comparison_pages(self):
        """
        Returns current active website AI comparison pages for this user
        """
        if self.current_active_website:
            return AIComparisonPage.objects.filter(website=self.current_active_website)
        return AIComparisonPage.objects.none()


# ===============================================================================

class HypestatData(models.Model):
    website = models.ForeignKey("Website", null=True, default=None, on_delete=models.CASCADE)
    domain = models.CharField(max_length=253, blank=False, null=False, db_index=True)
    organic_traffic = models.PositiveBigIntegerField(null=True)
    organic_keywords = models.PositiveBigIntegerField(null=True)
    domain_authority = models.PositiveBigIntegerField(null=True)
    total_backlinks = models.PositiveBigIntegerField(null=True)
    follow = models.PositiveBigIntegerField(null=True)
    no_follow = models.PositiveBigIntegerField(null=True)
    referring_domains = models.PositiveBigIntegerField(null=True)

    class Meta:
        unique_together = ('website', 'domain')  
    
    def __str__(self):
        return self.domain


class Keyword(models.Model):
    """
    Stores keyword data which is used in both Competitor and Website models.

    `keyword`, `source` & `country` impose a unique constraint together. All fields are required.
    """
    class Meta:
        indexes = [
            models.Index(fields=['keyword_md5_hash']),
            models.Index(fields=['keyword']),
            models.Index(fields=['keyword', 'source']),
            models.Index(fields=['keyword', 'country']),
            models.Index(fields=['keyword', 'source', 'country']),
        ]

    website = models.ForeignKey("Website", null=True, default=None, on_delete=models.CASCADE)

    # ---------------------------------------------------------------------------------------------
    # MD5 hash always has 32 hex characters (128 bit, 16 bytes)
    keyword_md5_hash = models.CharField(max_length=50, primary_key=True)

    # These three will always be unique together because of above keyword_md5_hash primary key field.
    # The hash value is calculated on content plan k8 job as an md5 hash of keyword + country + source value.
    keyword = models.CharField(max_length=300)
    source = models.CharField(max_length=100)
    country = models.CharField(max_length=100)
    # ---------------------------------------------------------------------------------------------

    serp_position = models.PositiveIntegerField(null=True)
    volume = models.PositiveIntegerField()
    cpc_currency = models.CharField(max_length=5)
    cpc_value = models.DecimalField(max_digits=6, decimal_places=2)
    paid_difficulty = models.DecimalField(max_digits=4, decimal_places=2)
    trend = models.JSONField(default=keywords_trend_default)
    kw_volume = models.BooleanField(default=False) 

    def __str__(self):
        return f"{self.keyword} - {self.country} - {self.source}"


class KeywordProject(models.Model):
    """
    Stores keyword research project data.
    """
    website = models.ForeignKey("Website", null=True, default=None, on_delete=models.CASCADE)
    project_name = models.CharField(max_length=300, blank=False, null=False)
    keywords = models.ManyToManyField(Keyword, db_index=True)
    total_traffic_volume = models.PositiveBigIntegerField(default=0)
    created_on = models.DateTimeField(default=timezone.now)
    project_id = models.CharField(max_length=300, unique=True, db_index=True)
    location_iso_code = models.CharField(max_length=2, blank=False, null=False)

    def __str__(self):
        return self.project_name


class AutomationProject(models.Model):
    """
    Stores Automation project data.
    """

    AUTO_PUBLISH_STATES = [
        ('on', "ON"),
        ('draft', "DRAFT"),
        ('off', "OFF"),
        ('paused', "PAUSED"),
        ('completed', "COMPLETED"),
    ]

    AUTO_PUBLISH_DAYS = [
        ('monday', 'Monday'),
        ('tuesday', 'Tuesday'),
        ('wednesday', 'Wednesday'),
        ('thursday', 'Thursday'),
        ('friday', 'Friday'),
        ('saturday', 'Saturday'),
        ('sunday', 'Sunday'),
    ]

    AUTO_PUBLISH_TIMES = [
        ('any_time', 'Any Time'),
        ('morning', 'Morning (06:00 AM to 12:00 PM)'),
        ('afternoon', 'Afternoon (12:00 PM to 06:00 PM)'),
        ('evening', 'Evening (06:00 PM to 12:00 AM)'),
        ('night', 'Night (12:00 AM to 06:00 AM)'),
    ]

    FREQUENCIES = [
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
    ]

    website = models.ForeignKey("Website", null=True, default=None, on_delete=models.CASCADE)
    project_id = models.CharField(max_length=300, unique=True, db_index=True)
    project_name = models.CharField(max_length=300, blank=True, null=True, default="Automation Project")
    associated_keyword_project = models.ForeignKey('KeywordProject', on_delete=models.SET_NULL, null=True, default=None)
    keywords_traffic_range_min = models.PositiveBigIntegerField(default=0)
    keywords_traffic_range_max = models.PositiveBigIntegerField(default=0)
    frequency = models.CharField(max_length=300, choices=FREQUENCIES, null=True, blank=False, default=None)
    article_count = models.PositiveIntegerField(default=2)
    selected_integration_name = models.CharField(max_length=300, null=True, blank=False, default=None)
    selected_integration_unique_text_id = models.TextField(null=True, default=None)
    auto_publish_state = models.CharField(max_length=100, null=False, blank=False, choices=AUTO_PUBLISH_STATES, default='off')
    auto_publish_days = ArrayField(models.CharField(max_length=300, choices=AUTO_PUBLISH_DAYS), default=list)
    auto_publish_time = models.CharField(max_length=300, choices=AUTO_PUBLISH_TIMES, default='any_time')
    auto_publish_email_hours = models.PositiveIntegerField(default=1)
    publish_only_generated_articles = models.BooleanField(default=True)
    created_on = models.DateTimeField(default=timezone.now)

    # these fields are updated by the automation job
    published_articles_count = models.PositiveIntegerField(default=0)
    last_ran_on = models.DateTimeField(null=True, default=None)
    articles_generated = models.BooleanField(default=False)

    @property
    def user(self):
        return self.website.user

    def clean(self):
        # Ensure keywords_traffic_range_max is greater than keywords_traffic_range_min
        if self.keywords_traffic_range_max < self.keywords_traffic_range_min:
            raise Exception("Maximum traffic range must be greater than minimum traffic range.")

    def __str__(self):
        return self.project_id


class IgnoredCompetitor(models.Model):
    """
    These domains will not be considered as (keyword based) competitors for any website.
    """
    domain = models.CharField(max_length=253, unique=True, blank=False, null=False)


class Competitor(models.Model):
    """
    Info on competitor of a website.
    """
    PROTOCOL_TYPES = [
        ('http', 'http'),
        ('https', 'https'),
    ]

    website = models.ForeignKey("Website", on_delete=models.CASCADE, null=True, default=None)
    name = models.CharField(max_length=253)
    domain = models.CharField(max_length=253, db_index=True)
    protocol = models.CharField(max_length=10, choices=PROTOCOL_TYPES, default="http")
    logo_url = models.CharField(max_length=1000)
    keywords = models.ManyToManyField(Keyword, db_index=True)
    keywords_generated = models.BooleanField(default=False)
    in_processing = models.BooleanField(default=False)
    associated_keyword_project = models.ForeignKey(KeywordProject, on_delete=models.SET_NULL, null=True, default=None)

    def __str__(self):
        return f"{self.protocol}://{self.domain}"


class WordpressIntegration(models.Model):
    """
    Stores Wordpress integration details.
    """
    class META:
        ordering = ['id']

    website = models.ForeignKey("Website", null=True, default=None, on_delete=models.CASCADE)
    site_url = models.TextField(blank=False, null=False)
    user_login = models.CharField(max_length=300, blank=False, null=False)
    password = models.CharField(max_length=300, blank=False, null=False)
    created_on = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return self.site_url

class WordpressCategories(models.Model):
    """
    Stores categories associated with WordPress integration.
    """
    class META:
        ordering = ['id']

    article_title = models.CharField(max_length=255, blank=False, null=False)  # The name of the category
    category_id = models.IntegerField(blank=False, null=False)  # The ID of the category

    def __str__(self):
        return f"{self.article_title} - {self.category_id}"


class WebflowIntegration(models.Model):
    """
    Stores Webflow integration details.
    """
    class META:
        ordering = ['id']

    BASED_ON = [
        ('app', "App Based"),
        ('api', "API Based")
    ]

    website = models.ForeignKey("Website", null=True, default=None, on_delete=models.CASCADE)
    based_on = models.CharField(max_length=10, choices=BASED_ON, default='app')
    token = models.CharField(max_length=300, blank=False, null=False)
    site_id = models.CharField(max_length=100, blank=False, null=False)
    site_url = models.TextField(blank=False, null=False)
    collection_id = models.CharField(max_length=100, blank=False, null=False)
    collection_slug = models.CharField(max_length=120, null=True)
    collection_fields = models.JSONField()
    created_on = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return self.site_url


class WixIntegration(models.Model):
    """
    Stores WIX integration details.
    """
    class META:
        ordering = ['id']

    website = models.ForeignKey("Website", null=True, default=None, on_delete=models.CASCADE)
    token = models.CharField(max_length=1000, blank=False, null=False)
    member_id = models.CharField(max_length=300, blank=False, null=False)
    site_id = models.CharField(max_length=300, blank=False, null=False)
    site_url = models.TextField(null=True, default=None)
    created_on = models.DateTimeField(default=timezone.now)

    def __str__(self):
        if self.site_url:
            return self.site_url
        else:
            return self.site_id


class ShopifyIntegration(models.Model):
    """
    Stores Shopify integration details.
    """
    class META:
        ordering = ['id']

    website = models.ForeignKey("Website", null=True, default=None, on_delete=models.CASCADE)
    shop_url = models.URLField()
    access_token = models.CharField(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.shop_url

class GhostIntegration(models.Model):
    """
    Stores Wordpress integration details.
    """
    class META:
        ordering = ['id']

    website = models.ForeignKey("Website", null=True, default=None, on_delete=models.CASCADE)
    site_url = models.TextField(blank=False, null=False)
    api_key = models.CharField(max_length=1000, blank=False, null=False)
    created_on = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return self.site_url



class Website(models.Model):
    """
    Stores info about the websites that are connected to Abun using our wordpress plugin.

    `domain` is the main unique (indexed) field.
    """
    PROTOCOL_TYPES = [
        ('http', 'http'),
        ('https', 'https'),
    ]

    KEYWORD_STRATEGIES = [
        ('volume', "Volume Based"),
        ('cpc', "CPC Based"),
        ('competition', "Competition Based"),
    ]

    FEATURED_IMAGE_TEMPLATE_IDS = [
        ('neon-style-with-text', 'Neon Style With Text'),
        ('neon-style-without-text', 'Neon Style Without Text'),
        ('water-color-with-text', 'Water Color With Text'),
        ('water-color-without-text', 'Water Color Without Text'),
        ('retro-with-text', 'Retro With Text'),
        ('retro-without-text', 'Retro Without Text'),
        ('comic-style-with-text', 'Comic Style With Text'),
        ('comic-style-without-text', 'Comic Style Without Text'),
        ('doodle-sketch-with-text', 'Doodle Sketch With Text'),
        ('doodle-sketch-without-text', 'Doodle Sketch Without Text'),
        ('cyberpunk-with-text', 'Cyberpunk With Text'),
        ('cyberpunk-without-text', 'Cyberpunk Without Text'),
        ('grunge-with-text', 'Grunge With Text'),
        ('grunge-without-text', 'Grunge Without Text'),
        ('water-color-with-doodle-with-text', 'Water With Doodle With Text'),
        ('water-color-with-doodle-without-text', 'Water With Doodle Without Text'),
        ('ok0l2K5mppOLZ3j1Yx', 'Elegant Minimalism'),
        ('j14WwV5Vjj4R5a7XrB', 'Compact Image Focus'),
        ('7wpnPQZzKKOm5dOgxo', 'Bold Blue Contrast'),
        ('yKBqAzZ9xwB0bvMx36', 'Image Dominance'),
        ('kY4Qv7D8dAaLDB0qmP', 'Bright Minimalism'),
    ]

    FEATURED_IMAGE_TEMPLATE_AND_LABLES = {
        'neon-style-with-text': "premium",
        'water-color-with-text': "premium",
        'retro-with-text': "premium",
        'comic-style-with-text': "premium",
        'doodle-sketch-with-text': "premium",
        'cyberpunk-with-text': "premium",
        'grunge-with-text': "premium",
        'water-color-with-doodle-with-text': "premium",
        'ok0l2K5mppOLZ3j1Yx': "basic",
        'kY4Qv7D8dAaLDB0qmP': "basic",
        'neon-style-without-text': "pro",
        'water-color-without-text': "pro",
        'retro-without-text': "pro",
        'comic-style-without-text': "pro",
        'water-color-with-doodle-without-text': "pro",
        'doodle-sketch-without-text': "pro",
        'cyberpunk-without-text': "pro",
        'grunge-without-text': "pro",
        'j14WwV5Vjj4R5a7XrB': "pro",
        '7wpnPQZzKKOm5dOgxo': "pro",
        'yKBqAzZ9xwB0bvMx36': "pro",
    }

    IMAGE_SOURCES = [
        ('no_image', "No Image"),
        ('unsplash', "Unsplash"),
        ('google', "Google Images"),
        ('ai_image_generation', "AI Image Generation"),
    ]

    IMAGES_FILE_FORMATS = [
        ('webp', 'WebP'),
        ('png', 'PNG'),
        ('jpeg', 'JPEG')
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)

    # we get these from the website connection api request
    domain = models.CharField(max_length=253, unique=True, db_index=True)
    name = models.CharField(max_length=300, null=False, blank=False, default="website_name")
    protocol = models.CharField(max_length=10, choices=PROTOCOL_TYPES, default="http")
    title = models.TextField(null=False, blank=True, default="")
    description = models.TextField(null=False, blank=True, default="")
    logo_url = models.TextField(null=False, blank=True, default="")  # clearbit logo url
    logo_dominant_color = models.CharField(max_length=10, null=False, blank=False, default="#000000")  # color hex code

    icp_text = models.TextField(null=False, blank=True, default="")
    industry = models.CharField(max_length=250, null=False, blank=True, default="")
    competitors = models.ManyToManyField(Competitor, db_index=True, related_name='websites_deprecated')
    regenerate_competitors = models.BooleanField(default=False)

    # preferences
    keyword_strategy = models.CharField(max_length=50, choices=KEYWORD_STRATEGIES, default='cpc')
    image_source = models.CharField(max_length=100, choices=IMAGE_SOURCES, default='no_image')
    feature_image_template_id = models.CharField(max_length=100, choices=FEATURED_IMAGE_TEMPLATE_IDS, default="water-color-with-text")
    feature_image_required = models.BooleanField(default=True)
    generate_bannerbear_featured_image = models.BooleanField(default=True)

    # show brand logo in featured image 
    show_logo_on_featured_image = models.BooleanField(default=False)
    
    # article tone of voice preference
    article_tone_of_voice = models.CharField(max_length=50, default="exciting")

    # article external backlinks preference
    external_backlinks_preference = models.CharField(max_length=50, default="no-follow")

    max_internal_backlinks = models.PositiveIntegerField(default=5)
    max_external_backlinks = models.PositiveIntegerField(default=2)

    # glossary internal backlinks
    internal_glossary_backlinks_preference = models.CharField(max_length=50, default="off")
    max_internal_glossary_backlinks = models.PositiveIntegerField(default=5)

    # article language preference
    article_language_preference = models.CharField(max_length=50, default="english")

    # article context
    article_context = models.TextField(null=False, blank=True, default="")

    # article language preference
    tone_of_article = models.CharField(max_length=50, default="off")

    # article language preference
    scale_of_tone = models.PositiveIntegerField(default=3)
    
    # active integration
    active_integration = models.CharField(null=True, default="")
    
    # Auto Indexing
    auto_indexing = models.BooleanField(default=False)
    last_auto_indexed_on = models.DateTimeField(null=True, blank=True)

    # article components
    toggle_toc = models.BooleanField(default=True)
    toggle_table = models.BooleanField(default=True)
    toggle_meta_description = models.BooleanField(default=True)
    toggle_faq = models.BooleanField(default=True)
    toggle_tldr = models.BooleanField(default=True)
    toggle_bullet_points = models.BooleanField(default=True)

    # ai_generated_image_style preference
    ai_generated_image_style = models.CharField(max_length=50, default="illustration")

    # Images File Format preference: WebP, PNG, or JPEG
    images_file_format = models.CharField(max_length=50, default="png", choices=IMAGES_FILE_FORMATS)

    # For Website Scanning
    finding_sitemaps = models.BooleanField(default=False)
    sitemap_urls = ArrayField(models.URLField(), default=list, null=True)
    crawling_started_on = models.DateTimeField(blank=True, null=True)
    crawling_ends_on = models.DateTimeField(blank=True, null=True)
    has_more_pages = models.BooleanField(default=False)
    task_queued = models.BooleanField(default=False)
    is_crawling = models.BooleanField(default=False)
    is_crawled = models.BooleanField(default=False)
    is_failed = models.BooleanField(default=False)
    auto_scan_website = models.BooleanField(default=False)  # Enable automatic daily scanning for new pages

    # For AI Auto Schema
    auto_schema_enabled = models.BooleanField(default=True)
    auto_schema_used = models.BooleanField(default=False)

    # For Tools Integration
    ai_auto_schema_script_verified = models.BooleanField(default=False)
    ai_calculator_script_verified = models.BooleanField(default=False)
    ai_stats_page_script_verified = models.BooleanField(default=False)
    ai_comparison_page_script_verified = models.BooleanField(default=False)
    ai_share_widget_script_verified = models.BooleanField(default=False)

    # ------ selected gsc domain ------
    selected_gsc_domain = models.CharField(default="")

    @property
    def feature_image_template_label(self) -> str:
        """
        Returns selected featured image template label
        """
        return self.FEATURED_IMAGE_TEMPLATE_AND_LABLES[self.feature_image_template_id]

    @property
    def crawling_status(self) -> str:
        """
        Returns crawling status "scanned" | "scanning" | "failed"
        """
        if self.is_crawling:
            return "scanning"
        elif self.finding_sitemaps:
            return "finding-sitemaps"
        elif self.is_crawled:
            return "scanned"
        elif self.task_queued:
            return "queued"
        elif self.is_failed:
            return "failed"
        else:
            return "not-started"

    @property
    def total_pages_scanned(self) -> int:
        """
        Returns total number of pages scanned
        """
        return self.webpage_set.all().count()

    @property
    def total_pages_without_schema(self) -> int:
        """
        Returns total number of pages without schema
        """
        return self.webpage_set.filter(schema_generated=False).count()

    @property
    def pages_with_auto_schema_live(self) -> int:
        """
        Returns total number of pages with auto schema live
        """
        if not self.ai_auto_schema_script_verified:
            return 0
        return self.webpage_set.filter(schema_enabled=True, schema_generated=True).count()

    @property
    def google_search_console_integrated(self) -> bool:
        """
        Google search console integrated
        """
        return bool(GoogleIntegration.objects.filter(website=self))

    @property
    def wp_integrated(self) -> bool:
        """
        Wordpress integrated
        """
        return bool(WordpressIntegration.objects.filter(website=self))

    def __str__(self):
        return f"{self.protocol}://{self.domain}"


class WebPage(models.Model):
    INCLUDE_LINKING_CHOICES = [
        ('on', 'on'),
        ('off', 'off'),
    ]

    website = models.ForeignKey(Website, on_delete=models.CASCADE)
    url = models.URLField(max_length=500)
    title = models.TextField()
    summary = models.TextField()
    embedding_id = models.CharField(max_length=250, null=True)  # Reference to ChromaDB
    include_linking = models.CharField(max_length=50, choices=INCLUDE_LINKING_CHOICES, default="on")
    created_on = models.DateTimeField(auto_now_add=True)
    last_modified_on = models.DateTimeField(blank=True, null=True)
    last_scraped_on = models.DateTimeField()
    excluded = models.BooleanField(default=False)  # Exclude from article internal linking

    # For Auto Schema
    schema = models.TextField(null=True, blank=True)
    schema_found = models.BooleanField(default=False)  # True if schema was found on the webpage (not generated, already present in the webpage)
    schema_enabled = models.BooleanField(default=True)  # Enable/Disable schema for this particular page
    schema_generated = models.BooleanField(default=False)
    schema_generating = models.BooleanField(default=False)
    schema_generation_failed = models.BooleanField(default=False)

    def __str__(self):
        return self.title

    class Meta:
        unique_together = ['website', 'url']


class FeaturedImage(models.Model):
    SOURCES = [
        ('defaultimage', "Default Image"),
        ('bannerbear', "Bannerbear"),
        ('ai_image_generation', "AI Image Generation"),
        ('uploaded_image', "Uploaded Image"),
    ]
    image_url = models.TextField(null=False, blank=False)
    template_id_used = models.CharField(max_length=100, default="")
    template_image_url = models.TextField(default="")
    source = models.CharField(max_length=300, choices=SOURCES)
    created_on = models.DateTimeField(default=timezone.now)

    def save(self, *args, **kwargs):
        if self.source not in [item[0] for item in self.SOURCES]:
            raise FeaturedImageModelError(self.article.article_uid, "'source' field value is wrong")

        super().save(*args, **kwargs)

    def __str__(self):
        return self.image_url


class WebsiteLogs(models.Model):
    """
    Saves website related logs
    """
    class META:
        ordering = ['date']

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    domain = models.CharField(max_length=253)
    date = models.DateTimeField(default=timezone.now)
    connection_type = models.CharField(max_length=100, null=True, blank=True, choices=[
        ('connected', 'connected'),
        ('disconnected', 'disconnected'),
    ])
    message = models.TextField()


class ArticleImage(models.Model):
    """
    Stores user uploaded article images
    """
    image = models.ImageField(upload_to=get_article_image_upload_to)
    created_on = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return self.image.url


class AIArticleImage(models.Model):
    """
    Stores AI generated article images
    """
    image_url = models.TextField(null=False, blank=False)
    context_title = models.TextField(null=False, blank=False)
    generated_context_description = models.TextField(null=False, blank=False)
    created_on = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return self.image_url


class Article(models.Model):
    """
    Stores info for each website article.
    """
    class Meta:
        ordering = ['id']

    POSTED_TO = [
        ('wordpress', "wordpress"),
        ('webflow', "webflow"),
        ('wix', "wix"),
        ('shopify', "shopify"),
    ]

    FEEDBACK = [
        ("positive", "Positive"),
        ("negative", "Negative"),
        ("no_feedback", "No Feedback"),
    ]

    POST_STATUS = [
        ('publish', 'publish'),
        ('draft', 'draft'),
    ]

    website = models.ForeignKey(Website, null=True, default=None, on_delete=models.CASCADE, db_index=True)

    article_uid = models.CharField(max_length=300, unique=True, db_index=True)

    title = models.TextField()
    keyword = models.ForeignKey(Keyword, null=True, on_delete=models.SET_NULL, default=None)
    url = models.TextField(blank=False, null=True, default=None)
    content = models.TextField(blank=False, null=True, default=None)
    article_description = models.TextField(blank=False, null=True, default=None)
    word_count = models.PositiveIntegerField(blank=False, null=True, default=None)
    image_count = models.PositiveIntegerField(blank=False, null=True, default=None)
    internal_link_count = models.PositiveIntegerField(blank=False, null=True, default=None)
    external_link_count = models.PositiveIntegerField(blank=False, null=True, default=None)
    selected_featured_image = models.OneToOneField(FeaturedImage, null=True, on_delete=models.SET_NULL)
    featured_images = models.ManyToManyField(FeaturedImage, related_name='general_article_featured_images')
    url_slug = models.TextField(blank=False, null=True, default=None)

    # Article generation output
    summary_inputs = models.JSONField(blank=True, null=True)
    merged_summary = models.TextField(blank=False, null=True)
    article_outlines = ArrayField(models.TextField(), default=list, null=True)
    crewai_output = models.TextField(default=None, null=True)

    article_images = models.ManyToManyField(ArticleImage)

    # stores the article post details
    article_link = models.TextField(blank=False, null=True, default=None)
    article_status = models.CharField(max_length=20, choices=POST_STATUS, default='draft')

    is_processing = models.BooleanField(default=False)
    is_failed = models.BooleanField(default=False)
    is_generated = models.BooleanField(default=False)
    is_posted = models.BooleanField(default=False)
    is_archived = models.BooleanField(default=False)
    is_user_added = models.BooleanField(default=False)

    # stores the platform where the article is posted
    posted_to = models.CharField(max_length=20, choices=POSTED_TO, default='wordpress')

    created_on = models.DateTimeField(default=timezone.now)
    generated_on = models.DateTimeField(null=True, default=None)
    posted_on = models.DateTimeField(null=True, default=None)

    other_top_ranking_urls = ArrayField(models.TextField(), default=list, null=True)

    # automation project, not required, can be null, is none by default
    associated_automation_project = models.ForeignKey(AutomationProject, on_delete=models.SET_NULL, null=True, default=None)

    # feedback from user, not required, can be null, is none by default
    feedback = models.CharField(max_length=20, choices=FEEDBACK, default='no_feedback', null=True)

    # stores additional context for the article, allowing for optional text input.
    context = models.TextField(null=True, default=None)

    # for internal links results
    internal_link_results = models.JSONField(blank=True, null=True)

    # for suggested internal links
    suggested_internal_links = models.JSONField(blank=True, null=True)
    
    # youtube video to article gen result.
    youtube_urls = models.JSONField(blank=True, null=True)
    transcript = models.TextField(blank=False, null=True, default=None)

    class ArticleObjectsManager(models.Manager):
        """
        Custom Article Objects Manager

        - The default behaviour is overrided so it's always going to return a filtered queryset where `is_archived` is always `False`
        - Use `all_queryset` method to get all articles
        - Use `archive_queryset` method to get all archive articles
        """
        archived = False

        def get_queryset(self):
            if self.archived is not None:
                return super().get_queryset().filter(is_archived=self.archived)
            else:
                return super().get_queryset()

        def all_queryset(self):
            self.archived = None
            return self.get_queryset()

        def archive_queryset(self):
            self.archived = True
            return self.get_queryset()

    # Custom Model Manager
    objects = ArticleObjectsManager()

    @property
    def user(self):
        return self.website.user

    def __str__(self):
        return self.article_uid


class KubernetesJob(models.Model):
    """
    Used to track info of a kubernetes job. `job_id` is the primary key and has to be provided
    using `generate_k8_job_id` function in utils.py
    """
    STATUS_LIST = [
        ('running', 'running'),
        ('completed', 'completed'),
        ('failed', 'failed'),
    ]

    job_id = models.CharField(max_length=100, primary_key=True, db_index=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    created_on = models.DateTimeField(default=timezone.now)

    status = models.CharField(max_length=20, choices=STATUS_LIST)
    fail_reason = models.TextField(default="", blank=True, null=False)
    retry_attempts = models.PositiveSmallIntegerField(default=0)

    # This can be used to hold other model id or data to be required later on.
    metadata = models.TextField(null=False, blank=True, default="")

    article_generation_cost_info = models.DecimalField(max_digits=10, decimal_places=6, default=0.0, null=True)

    def __str__(self):
        return self.job_id


class KubernetesJobLogs(models.Model):
    """
    Used to save logs/messages from various K8 Jobs for debugging purposes.
    """
    LOG_TYPES = [
        ('info', 'Info'),
        ('warning', 'Warning'),
        ('error', 'Error')
    ]

    job = models.ForeignKey(KubernetesJob, on_delete=models.CASCADE)
    created_on = models.DateTimeField(default=timezone.now)
    message = models.TextField()
    type = models.CharField(max_length=100, choices=LOG_TYPES)

    def save(self, *args, **kwargs):
        if self.type not in [item[0] for item in self.LOG_TYPES]:
            raise KubernetesLogError("KubernetesJobLogs Error: Value provided for 'type' field is not in LOG_TYPES")

        super().save(*args, **kwargs)

    def __str__(self):
        return self.message


# class ProTip(models.Model):
#     """
#     Stores tips shown on connect website loader page (and possibly other pages)
#     """
#     text = models.TextField(null=False, blank=False)

#     def __str__(self):
#         return self.text


class AdminStats(models.Model):
    """
    Saves stats for admin page.
    """
    class Meta:
        ordering = ['-created_on']

    created_on = models.DateTimeField(default=timezone.now)
    total_signups = models.IntegerField(null=True)
    total_paid_customer = models.IntegerField(null=True)
    total_free_customers = models.IntegerField(null=True)
    today_signup = models.IntegerField(null=True)
    today_paid_users = models.IntegerField(null=True)
    paid_users_weekly = models.IntegerField(null=True)

    def __str__(self):
        return self.created_on.strftime("%Y-%m-%d-%H-%M")


class WebsiteIndexation(models.Model):
    """
    Stores details about website indexation
    """
    SEARCH_ENGINES = [
        ('google', "Google")
    ]
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    website = models.OneToOneField(Website, on_delete=models.CASCADE)
    search_engine = models.CharField(max_length=50, choices=SEARCH_ENGINES)
    indexation_details = models.JSONField(null=True, default=None)
    completed = models.BooleanField(default=False)
    urls_sent_for_indexing = models.IntegerField(default=0)

    def __str__(self):
        return f"search-engine-{self.search_engine}-{self.website.domain}"


class Logo(models.Model):
    """
    Storage for website logos
    """
    domain = models.CharField(max_length=253, unique=True, db_index=True)
    image = models.ImageField(upload_to=get_logo_upload_to)

    def __str__(self):
        return self.domain


class GoogleIntegration(models.Model):
    """
    Stores Google integration details.
    """
    INTEGRATIONS = [
        ('google-search-console', "Google Search Console"),
        ('google-analytics', "Google Analytics"),
        ('google-drive', "Google Drive")
    ]

    website = models.OneToOneField("Website", on_delete=models.CASCADE, null=True, default=None)

    integration_type = models.CharField(max_length=50, choices=INTEGRATIONS)
    token = models.CharField(max_length=300)
    refresh_token = models.CharField(max_length=150)
    token_uri = models.CharField(max_length=50)
    client_id = models.CharField(max_length=100)
    client_secret = models.CharField(max_length=50)
    scopes = ArrayField(
        ArrayField(
            models.CharField(max_length=150)
        )
    )

    def __str__(self):
        return f"{self.website.user.email} - {self.integration_type}"


class AllArticlesStats(models.Model):
    """
    Stores stats for all articles generated or failed by all users.
    """
    generated_on = models.DateTimeField(default=timezone.now)
    is_successful = models.BooleanField(default=False)
    article_uid = models.CharField(max_length=300, unique=True, db_index=True)

    def __str__(self):
        return self.article_uid


class BlockDomain(models.Model):
    """
    These are the domains that we need to block at sign up and connect website

    Table Columns:
    | domain (CharField, max 100) - ex. example.com
    """

    class Meta:
        verbose_name_plural = "Blocked Domains"

    domain = models.CharField(max_length=100)

    def __str__(self):
        return self.domain


class BlockWebsiteKeywords(models.Model):
    """
    These are the website keywords that we need to block while connecting an website (for website)

    Table Columns:
    | keyword (CharField, max 100) - ex. draff
    """

    class Meta:
        verbose_name_plural = "Blocked Website Keywords"

    keyword = models.CharField(max_length=100)

    def __str__(self):
        return self.keyword


class BlockKeywords(models.Model):
    """
    These are the keywords that we need to block while doing keyword research
    Table Columns:
    | keyword (CharField, max 100) - ex. draff
    """

    class Meta:
        verbose_name_plural = "Blocked Keywords"

    keyword = models.CharField(max_length=300)

    def __str__(self):
        return self.keyword


class GPT4UsageStats(models.Model):
    """
    Stores stats for GPT4 usage
    """
    created_on = models.DateTimeField(default=timezone.now)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    usage_type = models.CharField(max_length=100)
    usage_cost = models.DecimalField(max_digits=10, decimal_places=6, default=0.0)

    def __str__(self):
        return self.user.email


class ScheduleArticlePosting(models.Model):
    """
    Stores schedule article posting details
    """
    POST_STATUS = [
        ('draft', "Draft"),
        ('publish', "Publish")
    ]

    article = models.OneToOneField(Article, on_delete=models.CASCADE)
    schedule_datetime = models.DateTimeField(auto_now_add=True)
    schedule_on = models.DateTimeField(auto_now_add=True)
    selected_integration_name = models.CharField(null=True, default=None)
    selected_integration_unique_text_id = models.TextField(null=True, default=None)
    post_status = models.CharField(max_length=10, choices=POST_STATUS, default='publish')


class SerperResults(models.Model):
    """
    Stores serper scraped data
    """

    keyword_hash = models.CharField(max_length=50)
    result = models.JSONField()

    def __str__(self) -> str:
        return f"Serper result for {self.keyword_hash}"


class BackLink(models.Model):
    """
    Store BackLinks data
    """
    class Meta:
        verbose_name_plural = "BackLinks"

    name = models.CharField(max_length=250)
    url = models.URLField(max_length=250)
    da_score = models.IntegerField()
    follow_unfollow_link = models.CharField(max_length=100)
    submissions = models.CharField(max_length=100)
    article_link = models.URLField(max_length=350)
    show_on_free_plan = models.BooleanField(default=False)
    def __str__(self) -> str:
        return f"{self.name} -- {self.url}"


class AutoCoupon(models.Model):
    """
    Table to store auto apply coupon code
    """
    name = models.CharField(max_length=100)
    coupon_code = models.CharField(max_length=100)
    enabled = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)


class Survey(models.Model):
    """
    Table to store survey data
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    survey_data = models.JSONField()
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.email} - {self.created_at}"


#--- seo programmatic model ----
class ProgrammaticSeoTitle(models.Model):
    """
    Table to store list of programmatic seo titles
    """
    website = models.ForeignKey(Website, null=True, default=None, on_delete=models.CASCADE, related_name="programmatic_seo_titles")
    keyword_project = models.ForeignKey(KeywordProject, on_delete=models.CASCADE, related_name="programmatic_seo_titles")
    created_at = models.DateTimeField(auto_now_add=True)
    articles = models.ManyToManyField(Article, related_name="programmatic_seo_titles", blank=True)
    is_processing = models.BooleanField(default=False)
    pattern = models.TextField(null=False, blank=True, default="")

    def __str__(self):
        return f"{self.keyword_project} - {self.user.email}"

class GlossaryTopic(models.Model):
    """
    Table to store list of glossary words
    """
    website = models.ForeignKey(Website, null=True, default=None, on_delete=models.CASCADE, related_name="glossary_topics")
    project_id = models.CharField(max_length=255,default="")
    created_at = models.DateTimeField(auto_now_add=True)
    word = models.TextField(null=False, blank=True, default="")
    glossary_words = ArrayField(models.TextField(), blank=True, default=list)

    def __str__(self):
        return f"Glossary - {self.project_id} - {self.word}"

class GlossaryContent(models.Model):

    POSTED_TO = [
        ('wordpress', "wordpress"),
        ('webflow', "webflow"),
        ('wix', "wix"),
        ('shopify', "shopify"),
    ]

    FEEDBACK = [
        ("positive", "Positive"),
        ("negative", "Negative"),
        ("no_feedback", "No Feedback"),
    ]

    website = models.ForeignKey(Website, null=True, default=None, on_delete=models.CASCADE, db_index=True)

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    project_id = models.CharField(max_length=255)
    topic = models.CharField(max_length=255)
    term = models.CharField(max_length=255)
    content = models.TextField()
    keyword_hash = models.CharField(max_length=32, unique=True)

    internal_link_count = models.PositiveIntegerField(blank=False, null=True, default=None)
    glossary_link = models.TextField(blank=False, null=True, default=None)
    is_processing = models.BooleanField(default=False)
    is_failed = models.BooleanField(default=False)
    is_generated = models.BooleanField(default=False)
    is_posted = models.BooleanField(default=False)
    is_archived = models.BooleanField(default=False)

    # stores the platform where the glossary is posted
    posted_to = models.CharField(max_length=20, choices=POSTED_TO, default='wordpress')
    created_at = models.DateTimeField(default=timezone.now)
    generated_on = models.DateTimeField(null=True, default=None)
    posted_on = models.DateTimeField(null=True, default=None)

    # feedback from user, not required, can be null, is none by default
    feedback = models.CharField(max_length=20, choices=FEEDBACK, default='no_feedback', null=True)
    task_id = models.CharField(max_length=255,null=True,blank=False)


    def __str__(self):
        return f"GlossaryContent - {self.term} - {self.keyword_hash}"

class ScheduleGlossaryPosting(models.Model):
    """
    Stores schedule article posting details
    """
    POST_STATUS = [
        ('draft', "Draft"),
        ('publish', "Publish")
    ]

    glossary = models.OneToOneField(GlossaryContent, on_delete=models.CASCADE)
    schedule_datetime = models.DateTimeField(auto_now_add=True)
    schedule_on = models.DateTimeField(auto_now_add=True)
    selected_integration_name = models.CharField(null=True, default=None)
    selected_integration_unique_text_id = models.TextField(null=True, default=None)
    post_status = models.CharField(max_length=10, choices=POST_STATUS, default='publish')


class AppSumoLicense(models.Model):
    """
    Table to store appsumo webhooks events data
    """
    USAGE_ALLOWED = {
        'tier_1': {
            'articles': 30,
            'keywords': 2000,
            'websites': 9999,
            'blog_automation': 2,
            'ai_calculator': 5,
            'ai_auto_schema': 100,
            'glossary_topic': 10,
            'glossary_word': 100,
            'blog_finder_email': 5,
            'guest_post_finder_query': 2,
            'reddit_post_finder_query': 2,
            'content_calendar': 9999,
            'search_console_insights': 9999,
            'fast_indexing': 9999,
            'amount_int': 59,
            'amount_string': "$59"
        },
        'tier_2': {
            'articles': 125,
            'keywords': 6000,
            'websites': 9999,
            'blog_automation': 2,
            'ai_calculator': 5,
            'ai_auto_schema': 100,
            'glossary_topic': 10,
            'glossary_word': 100,
            'blog_finder_email': 5,
            'guest_post_finder_query': 2,
            'reddit_post_finder_query': 2,
            'content_calendar': 9999,
            'search_console_insights': 9999,
            'fast_indexing': 9999,
            'amount_int': 149,
            'amount_string': "$149"
        },
        'tier_3': {
            'articles': 600,
            'keywords': 15000,
            'websites': 9999,
            'blog_automation': 2,
            'ai_calculator': 5,
            'ai_auto_schema': 100,
            'glossary_topic': 10,
            'glossary_word': 100,
            'blog_finder_email': 5,
            'guest_post_finder_query': 2,
            'reddit_post_finder_query': 2,
            'content_calendar': 9999,
            'search_console_insights': 9999,
            'fast_indexing': 9999,
            'amount_int': 349,
            'amount_string': "$349"
        },
    }

    license_key = models.CharField(max_length=300, unique=True)
    plan_id = models.CharField(max_length=300)
    license_status = models.CharField(max_length=50)
    created_on = models.DateTimeField(auto_now_add=True)
    activated_on = models.DateTimeField(null=True, default=None)
    deactivated_on = models.DateTimeField(null=True, default=None)
    next_renewal_date = models.DateTimeField()
    test_mode = models.BooleanField(default=True)
    tier = models.PositiveIntegerField(default=0)
    reason = models.TextField(default=None, null=True)

    @property
    def user(self) -> User | None:
        try:
            user = User.objects.get(appsumo_licenses__license_key=self.license_key)
        except User.DoesNotExist:
            return None

        return user

    @property
    def article_limit(self) -> int:
        tier_usage = self.USAGE_ALLOWED[f'tier_{self.tier}']
        return tier_usage['articles']

    @property
    def website_limit(self) -> int:
        tier_usage = self.USAGE_ALLOWED[f'tier_{self.tier}']
        return tier_usage['websites']

    @property
    def keyword_limit(self) -> int:
        tier_usage = self.USAGE_ALLOWED[f'tier_{self.tier}']
        return tier_usage['keywords']

    @property
    def title_limit(self) -> int:
        return 9999

    @property
    def ai_calculator_limit(self) -> int:
        tier_usage = self.USAGE_ALLOWED[f'tier_{self.tier}']
        return tier_usage['ai_calculator']

    @property
    def ai_auto_schema_limit(self) -> int:
        tier_usage = self.USAGE_ALLOWED[f'tier_{self.tier}']
        return tier_usage['ai_auto_schema']

    @property
    def glossary_topic_limit(self) -> int:
        tier_usage = self.USAGE_ALLOWED[f'tier_{self.tier}']
        return tier_usage['glossary_topic']

    @property
    def glossary_word_limit(self) -> int:
        tier_usage = self.USAGE_ALLOWED[f'tier_{self.tier}']
        return tier_usage['glossary_word']

    @property
    def blog_finder_email_limit(self) -> int:
        tier_usage = self.USAGE_ALLOWED[f'tier_{self.tier}']
        return tier_usage['blog_finder_email']

    @property
    def guest_post_finder_query_limit(self) -> int:
        tier_usage = self.USAGE_ALLOWED[f'tier_{self.tier}']
        return tier_usage['guest_post_finder_query']

    @property
    def reddit_post_finder_query_limit(self) -> int:
        tier_usage = self.USAGE_ALLOWED[f'tier_{self.tier}']
        return tier_usage['reddit_post_finder_query']

    @property
    def blog_automation_limit(self) -> int:
        tier_usage = self.USAGE_ALLOWED[f'tier_{self.tier}']
        return tier_usage['blog_automation']

    @property
    def content_calendar_limit(self) -> int:
        tier_usage = self.USAGE_ALLOWED[f'tier_{self.tier}']
        return tier_usage['content_calendar']

    @property
    def search_console_insights_limit(self) -> int:
        tier_usage = self.USAGE_ALLOWED[f'tier_{self.tier}']
        return tier_usage['search_console_insights']

    @property
    def fast_indexing_limit(self) -> int:
        tier_usage = self.USAGE_ALLOWED[f'tier_{self.tier}']
        return tier_usage['fast_indexing']

    @property
    def ai_auto_schema_limit(self) -> int:
        tier_usage = self.USAGE_ALLOWED[f'tier_{self.tier}']
        return tier_usage['ai_auto_schema']

    @property
    def plan_amount_string(self) -> str:
        tier_usage = self.USAGE_ALLOWED[f'tier_{self.tier}']
        return tier_usage['amount_string']

    @property
    def plan_amount_int(self) -> int:
        tier_usage = self.USAGE_ALLOWED[f'tier_{self.tier}']
        return tier_usage['amount_int']

    def __str__(self) -> str:
        if self.user:
            return f"{self.license_key} - {self.user.email}"
        return self.license_key

class BlogFinder(models.Model):
    """
    Tabel to store blog url details
    """
    website = models.ForeignKey(Website, null=True, default=None, on_delete=models.CASCADE, related_name="blog_finder")
    blog_url = models.TextField(null=True, default=None)
    is_valid = models.BooleanField(default=False)
    author_name = models.CharField(max_length=300)
    email_address = models.CharField(max_length=300)
    blog_m5_hash = models.CharField(max_length=50, primary_key=True, default='')
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.author_name}"


class BlogFinderProject(models.Model):
    """
    Project table for blog urls
    """
    website = models.ForeignKey(Website, null=True, default=None, on_delete=models.CASCADE, related_name="blog_finder_project")
    blog_project_id = models.CharField(max_length=300, unique=True, db_index=True)
    blog_project_name = models.CharField(max_length=500)
    blog_finder = models.ManyToManyField(BlogFinder, db_index=True, related_name="blog_finder_project")
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.blog_project_name


class GuestPostFinderQuery(models.Model):
    "Table to store guest post finder results"
    website = models.ForeignKey("Website", null=True, on_delete=models.CASCADE)
    guest_project_id = models.CharField(max_length=300, unique=True, db_index=True, null=True, blank=True)
    query = models.CharField(max_length=250)
    limit = models.IntegerField()
    is_processing = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.website.user.username} -- {self.query}"


class GuestPostFinderResult(models.Model):
    "table to store guest finder result"
    post_title = models.CharField(max_length=500, blank=True)
    post_link = models.CharField(max_length=500, blank=True)
    guest_post_finder = models.ForeignKey(GuestPostFinderQuery, on_delete=models.CASCADE)
    hypestat = models.ForeignKey(HypestatData, on_delete=models.CASCADE, blank=True)

    def __str__(self):
        return f"{self.guest_post_finder.query} -- {self.post_title}"


class RedditPostFinderQuery(models.Model):
    "Table to store reddit post finder results"
    website = models.ForeignKey("Website", null=True, on_delete=models.CASCADE)
    reddit_project_id = models.CharField(max_length=300, unique=True, db_index=True, null=True, blank=True)
    query = models.CharField(max_length=250)
    limit = models.IntegerField()
    is_processing = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.website.user.username} -- {self.query}"


class RedditPostFinderResult(models.Model):
    "table to store reddit post finder result"
    post_title = models.CharField(max_length=500, blank=True)
    post_link = models.CharField(max_length=500, blank=True)
    reddit_post_finder = models.ForeignKey(RedditPostFinderQuery, on_delete=models.CASCADE)
    position = models.PositiveBigIntegerField(null=True)
    subreddit_name = models.CharField(max_length=255, blank=True)
    upvote_score = models.IntegerField(null=True, blank=True)
    upvote_ratio = models.FloatField(null=True, blank=True)
    num_comments = models.IntegerField(null=True, blank=True)
    created_utc = models.DateTimeField(null=True, blank=True)
    subreddit_subscribers = models.IntegerField(null=True, blank=True)
    reddit_content = models.TextField(blank=True)

    def __str__(self):
        return f"{self.reddit_post_finder.query} -- {self.post_title}"


class GSCKeywordStatus(models.Model):
    keyword = models.CharField(max_length=255)
    generated = models.BooleanField(default=False)
    processing = models.BooleanField(default=False)
    failed = models.BooleanField(default=False)
    project_id = models.CharField(max_length=255, unique=True)
    keyword_hash = models.CharField(max_length=255,null=True)
    created_at = models.DateTimeField(default=timezone.now)
    website = models.ForeignKey("Website", null=True, default=None, on_delete=models.CASCADE)

    def __str__(self):
        return f"{self.keyword} - Project: {self.project_id}"


class ChangeLog(models.Model):
    "table to store abun changelogs"
    title = models.CharField(max_length=500)
    description = models.TextField(max_length=2000)
    created_at = models.DateTimeField()

    def __str__(self):
        return f"{self.title}"


class WebsiteScanQueue(models.Model):
    """
    Queue for website scanning tasks
    """
    STATUS_CHOICES = [
        ('queued', 'Queued'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed')
    ]

    RUN_CHOICES = [
        ('generate-schema', 'Generate Schema'),
        ('generate-summary', 'Generate Summary'),
        ('generate-seo-data', 'Generate SEO Data'),
        ('all', 'All Tools')
    ]

    website = models.ForeignKey(Website, on_delete=models.CASCADE)
    sitemap_urls = ArrayField(models.URLField(), default=list, null=True)
    website_urls = ArrayField(ArrayField(models.URLField()), default=list, null=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='queued')
    # Which tool to run for the website scanning task
    tool_to_run = models.CharField(max_length=20, choices=RUN_CHOICES, default='generate-summary')
    created_at = models.DateTimeField(auto_now_add=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(null=True, blank=True)
    request_from_admin = models.BooleanField(default=False)

    class Meta:
        ordering = ['created_at']

    def __str__(self):
        return f"Scan Queue - {self.website.domain} ({self.status})"


class InstructionAndContext(models.Model):
    """
    Context for article generation
    """
    website = models.ForeignKey(Website, on_delete=models.CASCADE)
    context = models.TextField(null=True, default=None)
    created_on = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        if self.context:
            return " ".join(self.context.split()[:3])
        return "No context available"


class AICalculatorVersion(models.Model):
    """
    Stores Generated AI calculator versions and code
    """
    calculator = models.ForeignKey("AICalculator", on_delete=models.CASCADE)
    version_name = models.CharField(max_length=100)
    html_code = models.TextField()
    created_on = models.DateTimeField(default=timezone.now)

    class META:
        ordering = ['created_on']
        unique_together = ['calculator', 'version_name']

    def __str__(self):
        return f"{self.calculator.calculator_id} - {self.version_name}"


class AICalculator(models.Model):
    """
    Stores Generated AI calculator Info
    """
    STATUS = [
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('modifying', 'Modifying')
    ]

    # Relation to Website model
    website = models.ForeignKey(Website, on_delete=models.CASCADE)

    # Stores calculator info
    calculator_id = models.CharField(max_length=100, unique=True)
    calc_type = models.CharField(max_length=100)
    calc_description = models.TextField(blank=True, null=True)
    conversation = models.JSONField()
    created_on = models.DateTimeField(default=timezone.now)
    generation_status = models.CharField(choices=STATUS, max_length=20, default='processing')

    @property
    def encrypted_tool_id(self):
        """
        Returns the encrypted tool ID
        """
        # Encrypt the website ID using the same method as AICalculator
        key = base64.urlsafe_b64encode(hashlib.sha256(settings.SECRET_KEY.encode()).digest())

        # Create a Fernet instance with the key
        f = Fernet(key)

        # Encrypt the website ID
        encrypted_id = f.encrypt(str(self.calculator_id).encode()).decode()
        return encrypted_id

    @property
    def html_code(self):
        """
        Returns the HTML code for the calculator.
        """
        if not self.aicalculatorversion_set.exists():
            return None

        return self.aicalculatorversion_set.last().html_code

    @html_code.setter
    def html_code(self, value: str):
        """
        Saves the HTML code for the calculator.
        """
        try:
            version_name: str = f"{self.calc_type}-v{self.aicalculatorversion_set.count() + 1}"
            AICalculatorVersion.objects.create(calculator=self, version_name=version_name, html_code=value)
        except ValueError:
            version_name = f"{self.calc_type}-v1"
            AICalculatorVersion.objects.create(calculator=self, version_name=version_name, html_code=value)

    def __str__(self):
        return f"{self.calc_type}-{self.calculator_id}"


class AIStreamingToken(models.Model):
    """
    Table to store AI streaming tokens
    """
    token = models.CharField(max_length=255, unique=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    used = models.BooleanField(default=False)

    def is_valid(self):
        return timezone.now() < self.expires_at and not self.used


class GHLIntegration(models.Model):
    """
    GHL integration
    """
    class META:
        ordering = ['id']

    website = models.ForeignKey("Website", null=True, default=None, on_delete=models.CASCADE)
    access_token = models.CharField(max_length=5000)
    refresh_token = models.CharField(max_length=5000)
    location_id = models.CharField(max_length=50, null=False)
    site_id = models.CharField(max_length=50, null=False)
    ghl_domain = models.CharField(max_length=100, null=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.site_id


class GHLCategories(models.Model):
    """
    Stores categories associated with ghl integration.
    """
    class META:
        ordering = ['id']

    article_title = models.CharField(max_length=255, blank=False, null=False)  # The name of the category
    category_id = models.CharField(blank=False, null=False)  # The ID of the category

    def __str__(self):
        return f"{self.article_title} - {self.category_id}"


class ArticleGenerationQueue(models.Model):
    """
    Queue for article generation tasks in production environment
    """
    STATUS_CHOICES = [
        ('queued', 'Queued'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed')
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    article = models.ForeignKey(Article, on_delete=models.CASCADE)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='queued')
    regenerate = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    submitted_at = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(null=True, blank=True)

    class Meta:
        ordering = ['created_at']

    def __str__(self):
        return f"Article Generation Queue - {self.article.article_uid} ({self.status})"


class ArticleInternalLinkQueue(models.Model):
    """
    Queue for article internal linking tasks for articles
    """
    STATUS_CHOICES = [
        ('queued', 'Queued'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed')
    ]

    article = models.ForeignKey(Article, on_delete=models.CASCADE)
    internal_link_keywords = models.JSONField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='queued')
    created_at = models.DateTimeField(auto_now_add=True)
    k8_job = models.ForeignKey(KubernetesJob, on_delete=models.CASCADE)
    error_message = models.TextField(null=True, blank=True)

    class Meta:
        ordering = ['created_at']

    def __str__(self):
        return f"Article Internal Link Queue - {self.article.title} ({self.status})"


class WordpressPublishedArticle(models.Model):
    """
    Stores info about published WordPress articles pulled into the system.
    """
    website = models.ForeignKey("Website", null=True, default=None, on_delete=models.CASCADE)
    article = models.ForeignKey("Article", null=True, on_delete=models.SET_NULL, related_name="wordpress_published")

    post_id = models.IntegerField(null=False, db_index=True)
    media_id = models.IntegerField(null=True, blank=True)

    title = models.CharField(max_length=500)
    slug = models.SlugField(max_length=255)
    url = models.URLField()
    published_date = models.DateTimeField()
    gsc_position = models.FloatField(null=True, blank=True)

    created_on = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('website', 'post_id')
        ordering = ['-published_date']

    def __str__(self):
        return f"{self.title} (Post ID: {self.post_id})"


class AIStatsPageVersion(models.Model):
    """
    Stores Generated AI stats page versions and code
    """
    stats_page = models.ForeignKey("AIStatsPage", on_delete=models.CASCADE)
    version_name = models.CharField(max_length=150)
    html_code = models.TextField()
    changes_summary = models.TextField(blank=True, null=True)
    created_on = models.DateTimeField(default=timezone.now)
    is_current = models.BooleanField(default=False, help_text="Indicates if this is the current active version")

    class Meta:
        ordering = ['-created_on']
        unique_together = ['stats_page', 'version_name']

    def __str__(self):
        return f"{self.stats_page.stats_id} - {self.version_name}"

    def save(self, *args, **kwargs):
        if self.is_current:
            AIStatsPageVersion.objects.filter(
                stats_page=self.stats_page, 
                is_current=True
            ).update(is_current=False)
        super().save(*args, **kwargs)


class AIStatsPage(models.Model):
    """
    Stores Generated AI stats page Info
    """
    website = models.ForeignKey('Website', on_delete=models.CASCADE)

    stats_id = models.CharField(max_length=100, unique=True)
    stats_type = models.CharField(max_length=100)
    stats_topic = models.CharField(max_length=500)
    stats_description = models.TextField(blank=True, null=True)
    conversation = models.JSONField(default=list, blank=True)

    original_keyword = models.CharField(max_length=200, blank=True, null=True)
    selected_idea = models.JSONField(default=dict, blank=True) 
    
    created_on = models.DateTimeField(default=timezone.now)
    updated_on = models.DateTimeField(auto_now=True)
    current_version_id = models.IntegerField(null=True, blank=True)

    class Meta:
        ordering = ['-created_on']

    def get_selected_idea_title(self):
        """Returns the title of the selected idea"""
        if self.selected_idea and isinstance(self.selected_idea, dict):
            return self.selected_idea.get('title', '')
        return ''
    
    def get_selected_idea_description(self):
        """Returns the description of the selected idea"""
        if self.selected_idea and isinstance(self.selected_idea, dict):
            return self.selected_idea.get('description', '')
        return ''

    @property
    def html_code(self):
        """
        Returns the HTML code for the current version of the stats page.
        """
        current_version = self.get_current_version()
        return current_version.html_code if current_version else None

    @html_code.setter
    def html_code(self, value: str):
        """
        Saves the HTML code for the stats page as a new version.
        """
        try:
            version_count = self.aistatspageversion_set.count()
            version_name = f"{self.stats_type[:90]}-v{version_count + 1}"
        except:
            version_name = f"{self.stats_type}-v1"
            
        new_version = AIStatsPageVersion.objects.create(
            stats_page=self, 
            version_name=version_name, 
            html_code=value,
            is_current=True
        )
        
        return new_version

    def get_current_version(self):
        """
        Returns the current version based on current_version_id field
        """
        if self.current_version_id:
            try:
                return self.aistatspageversion_set.get(id=self.current_version_id)
            except AIStatsPageVersion.DoesNotExist:
                pass
        
        return self.aistatspageversion_set.first()
    
    def get_version_count(self):
        """
        Returns the total number of versions
        """
        return self.aistatspageversion_set.count()

    def __str__(self):
        return f"{self.stats_type}-{self.stats_id}"


class AIComparisonPageVersion(models.Model):
    """
    Stores Generated AI comparison page versions and code
    """
    comparison_page = models.ForeignKey("AIComparisonPage", on_delete=models.CASCADE)
    version_name = models.CharField(max_length=150)
    html_code = models.TextField()
    changes_summary = models.TextField(blank=True, null=True)
    created_on = models.DateTimeField(default=timezone.now)
    is_current = models.BooleanField(default=False, help_text="Indicates if this is the current active version")

    class Meta:
        ordering = ['-created_on']
        unique_together = ['comparison_page', 'version_name']

    def __str__(self):
        return f"{self.comparison_page.comp_id} - {self.version_name}"

    def save(self, *args, **kwargs):
        if self.is_current:
            AIComparisonPageVersion.objects.filter(
                comparison_page=self.comparison_page, 
                is_current=True
            ).update(is_current=False)
        super().save(*args, **kwargs)


class AIComparisonPage(models.Model):
    """
    Stores Generated AI comparison page Info
    """
    website = models.ForeignKey('Website', on_delete=models.CASCADE)

    comp_id = models.CharField(max_length=100, unique=True)
    comparison_type = models.CharField(max_length=100)
    url1 = models.URLField(max_length=500)
    url2 = models.URLField(max_length=500)
    conversation = models.JSONField(default=list, blank=True)

    created_on = models.DateTimeField(default=timezone.now)
    updated_on = models.DateTimeField(auto_now=True)
    current_version_id = models.IntegerField(null=True, blank=True)

    class Meta:
        ordering = ['-created_on']

    @property
    def html_code(self):
        """
        Returns the HTML code for the current version of the comparison page.
        """
        current_version = self.get_current_version()
        return current_version.html_code if current_version else None

    @html_code.setter
    def html_code(self, value: str):
        """
        Saves the HTML code for the comparison page as a new version.
        """
        try:
            version_count = self.aicomparisonpageversion_set.count()
            version_name = f"{self.comparison_type[:90]}-v{version_count + 1}"
        except:
            version_name = f"{self.comparison_type}-v1"
            
        new_version = AIComparisonPageVersion.objects.create(
            comparison_page=self, 
            version_name=version_name, 
            html_code=value,
            is_current=True
        )
        
        return new_version

    def get_current_version(self):
        """
        Returns the current version based on current_version_id field
        Falls back to latest version if current_version_id is not set or invalid
        """
        if self.current_version_id:
            try:
                return self.aicomparisonpageversion_set.get(id=self.current_version_id)
            except AIComparisonPageVersion.DoesNotExist:
                pass
        
        return self.aicomparisonpageversion_set.first()
    
    def get_version_count(self):
        """
        Returns the total number of versions
        """
        return self.aicomparisonpageversion_set.count()

    def __str__(self):
        return f"{self.comparison_type}-{self.comp_id}"


class AIShareWidget(models.Model):
    """Table to store AI share widgets"""
    STYLE_CHOICES = [
        ('Horizontal with Logos', 'Horizontal with Logos'),
        ('Stacked with Logos', 'Stacked with Logos'),
        ('Horizontal with Icon', 'Horizontal with Icon'),
        ('Stacked with Icon', 'Stacked with Icon'),
        ('Horizontal with Buttons', 'Horizontal with Buttons'),
    ]
    
    LLM_CHOICES = [
        ('chatgpt', 'ChatGPT'),
        ('claude', 'Claude'),
        ('google-ai', 'Google AI'),
        ('grok', 'Grok'),
        ('perplexity', 'Perplexity'),
    ]
    
    website = models.ForeignKey("Website", null=True, on_delete=models.CASCADE)
    widget_id = models.CharField(max_length=300, unique=True, db_index=True)
    name = models.CharField(max_length=250)
    prompt_template = models.TextField()
    text_before_button = models.CharField(max_length=500, default="Summarize & Talk with this Page on :", blank=True)
    selected_llms = models.JSONField(default=list)
    style = models.CharField(max_length=50, choices=STYLE_CHOICES, default='Horizontal with Logos')
    html_code = models.TextField(blank=True, default='')
    html_code_dark = models.TextField(blank=True, default='')
    is_processing = models.BooleanField(default=True)  
    is_active = models.BooleanField(default=True)
    total_clicks = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.website.user.username} -- {self.name}"
    
    class Meta:
        ordering = ['-created_at']


class AIShareWidgetClick(models.Model):
    """Table to store widget click analytics"""
    widget = models.ForeignKey(AIShareWidget, on_delete=models.CASCADE, related_name='clicks')
    llm = models.CharField(max_length=50)
    destination_url = models.URLField()
    clicked_at = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(null=True, blank=True)

    def __str__(self):
        return f"{self.widget.name} -- {self.llm} -- {self.destination_url}"

    class Meta:
        ordering = ['-clicked_at']
