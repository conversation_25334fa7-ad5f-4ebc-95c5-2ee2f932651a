import os
import json
import base64
import hashlib
import logging
import re
import time
from typing import Dict

from django.core.handlers.wsgi import WSGIRequest
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.clickjacking import xframe_options_exempt
from django.http import JsonResponse, HttpResponse, StreamingHttpResponse

import openai

import tldextract
import cryptography.fernet
from rest_framework.request import Request
from rest_framework.decorators import api_view
from cryptography.fernet import Fernet, InvalidToken

from AbunDRFBackend import settings
from mainapp.utils import decrypt_dict
from mainapp.serializers import ChangeLogSerializer
from mainapp.json_responses import JsonResponseBadRequest
from mainapp.models import ChangeLog, AICalculator, AIStreamingToken, User, Website, WebPage, AIStatsPage, AIComparisonPage, AIShareWidget


logger = logging.getLogger(__name__)

@api_view(['GET'])
def get_changelogs(request: Request):
    """
    Get Changelogs.

    :param request: Django Rest Framework's Request object.
    """
    return JsonResponse(
        status=200,
        data=ChangeLogSerializer(ChangeLog.objects.all().order_by("-created_at"), many=True).data,
        safe=False,
    )


@csrf_exempt
def stream_ai_response(request: WSGIRequest, token: str):
    """
    Streams the AI response
    :param request: Django Rest Framework's Request object
    :param token: Valid AI streaming token
    """
    if request.method == "POST":
        try:
            ai_streaming_token = AIStreamingToken.objects.get(token=token)
        except AIStreamingToken.DoesNotExist:
            return JsonResponseBadRequest(additional_data={"err_id": 'INVALID_TOKEN', "message": "Invalid token"})

        if not ai_streaming_token.is_valid():
            return JsonResponseBadRequest(additional_data={"err_id": 'EXPIRED_TOKEN', "message": "Token expired or invalid"})

        # get the payload from the body
        payload: Dict = json.loads(request.body.decode())

        # update the prompt if its a translation request
        translation_matching_prompt = """
        请帮我翻译以上内容，在翻译之前，想先判断一下这个内容是不是中文，如果是中文，则翻译问英文，如果是其他语言，则需要翻译为中文，注意，你只需要返回翻译的结果，不需要对此进行任何解释，不需要除了翻译结果以外的其他任何内容
        """.replace("\n", "").strip()

        updated_prompt = """
        Please help me translate the above content. Before translating, I want to determine whether the content is in English (US). If it is in English (US), translate it into English (UK). If it is in other languages, translate it into English (US). Please note that you only need to return the translation result, and you do not need to explain it or any other content except the translation result.
        """.replace("\n", "").strip()

        if translation_matching_prompt in payload['messages'][0]['content']:
            payload['messages'][0]['content'] = payload['messages'][0]['content'].replace(translation_matching_prompt, updated_prompt)

        def generate():
            client = openai.Client()
            try:
                # Create the streaming completion with the new API
                stream = client.chat.completions.create(**payload)
            except openai._exceptions.InternalServerError:
                return JsonResponseBadRequest(additional_data={"err_id": 'SERVER_ERROR', "message": "Server error"})

            for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    yield f"data: {json.dumps({'choices': [{'delta': {'content': chunk.choices[0].delta.content}}]})}\n\n"

        # mark the token as used
        ai_streaming_token.used = True
        ai_streaming_token.save()

        return StreamingHttpResponse(generate(), content_type="text/event-stream")

    return JsonResponseBadRequest(additional_data={"err_id": 'METHOD_NOT_ALLOWED', "message": "Requested method is not allowed"})


@api_view(['OPTIONS', 'GET'])
def load_tools_scripts(request: Request):
    """
    API view to generate a customized JavaScript for loading all tools.
    :param request: Django Rest Framework's Request object.
    """
    try:
        encrpted_user_email: str = request.query_params["user-id"]
        page_url: str = request.query_params["url"]
        tool_name: str = request.query_params["tool-name"]
    except KeyError as key:
        logger.error(f"Missing required parameters in load_tools_scripts API: {key}")
        return JsonResponseBadRequest(additional_data={'err_id': "MISSING_PARAMETERS"})

    # Get the tool id if provided
    encrpted_tool_id: str = request.query_params.get("tool-id")

    # Decrypt the user email
    key = base64.urlsafe_b64encode(hashlib.sha256(settings.SECRET_KEY.encode()).digest())
    f = Fernet(key)

    # Decrypt the user email
    try:
        user_email = f.decrypt(encrpted_user_email.encode()).decode()
    except InvalidToken:
        logger.error(f"Invalid user email token: {encrpted_user_email}")
        return JsonResponseBadRequest(additional_data={'err_id': "INVALID_USER_EMAIL"})
    except Exception as e:
        logger.critical(f"Error decrypting user email: {str(e)}")
        return JsonResponseBadRequest(additional_data={'err_id': "INVALID_USER_EMAIL"})

    if encrpted_tool_id and encrpted_tool_id not in ["", "null", "undefined"]:
        # Decrypt the tool ID
        try:
            tool_id = f.decrypt(encrpted_tool_id.encode()).decode()
        except InvalidToken:
            logger.error(f"Invalid tool ID token: {encrpted_tool_id}")
            return JsonResponseBadRequest(additional_data={'err_id': "INVALID_TOOL_ID"})
        except Exception as e:
            logger.critical(f"Error decrypting tool ID: {str(e)}")
            return JsonResponseBadRequest(additional_data={'err_id': "INVALID_TOOL_ID"})

    else:
        tool_id = None

    # Get the user
    try:
        user: User = User.objects.get(email=user_email)
    except User.DoesNotExist:
        logger.error(f"No user found with {user_email} email.")
        return JsonResponseBadRequest(additional_data={'err_id': "NO_USER_FOUND"})

    # Extract the domain from the page url
    domain_extract: str = tldextract.extract(page_url)

    if domain_extract.subdomain:
        domain: str = f"{domain_extract.subdomain}.{domain_extract.domain}.{domain_extract.suffix}"
    else:
        domain: str = f"{domain_extract.registered_domain}"

    # Get the website
    try:
        website: Website = user.website_set.get(domain=domain)
    except Website.DoesNotExist:
        logger.error(f"No website found with {domain} domain.")
        return JsonResponseBadRequest(additional_data={'err_id': "NO_WEBSITE_FOUND"})

    # List to store all script contents
    script_contents = []

    if tool_name == "ai-auto-schema" and website.ai_auto_schema_script_verified and website.auto_schema_enabled:
        # Auto Schema Tool - load if website has webpages
        # Get the webpage
        try:
            webpage: WebPage = website.webpage_set.get(url=page_url)
        except WebPage.DoesNotExist:
            logger.error(f"No webpage found with {page_url} url.")
            webpage = None

        if webpage and webpage.schema_enabled:
            try:
                auto_schema_script_path = os.path.join(settings.BASE_DIR, 'tools_scripts', 'auto_schema', 'auto_schema_tool.js')

                if os.path.exists(auto_schema_script_path):
                    with open(auto_schema_script_path, 'r') as file:
                        auto_schema_content = file.read()

                        # Convert schema to string
                        schema_string = json.dumps(webpage.schema)

                        # Replace placeholders
                        auto_schema_content = auto_schema_content.replace("'JSONLD_SCHEMA'", schema_string)
                        script_contents.append(auto_schema_content)

                else:
                    logger.error(f"Auto schema script not found at {auto_schema_script_path}")

            except Exception as e:
                logger.critical(f"Error loading auto schema script: {str(e)}")

    if tool_name == "ai-calculator" and website.ai_calculator_script_verified and tool_id:
        # Calculator Tool
        # Get the calculator instance
        try:
            ai_calculator: AICalculator = user.ai_calculators.get(calculator_id=tool_id)
        except AICalculator.DoesNotExist:
            logger.error(f"No calculator found with {tool_id} ID.")
            ai_calculator = None

        if ai_calculator:
            try:
                ai_calculator_script_path = os.path.join(settings.BASE_DIR, 'tools_scripts', 'ai_calculator', 'calculator_tool.js')

                if os.path.exists(ai_calculator_script_path):
                    with open(ai_calculator_script_path, 'r') as file:
                        ai_calculator_content = file.read()

                        # Replace placeholders
                        ai_calculator_content = ai_calculator_content.replace("CALCULATOR_ID", encrpted_tool_id)
                        ai_calculator_content = ai_calculator_content.replace("'HTML_CONTENT'", json.dumps(ai_calculator.html_code))
                        script_contents.append(ai_calculator_content)

                else:
                    logger.error(f"Auto schema script not found at {auto_schema_script_path}")

            except Exception as e:
                logger.critical(f"Error loading auto schema script: {str(e)}")

    if tool_name == "ai-stats-page" and website.ai_stats_page_script_verified and tool_id:
        pass

    if tool_name == "ai-comparison-page" and website.ai_comparison_page_script_verified and tool_id:
        pass

    if tool_name == "ai-share-widget" and website.ai_share_widget_script_verified and tool_id:
        pass

    # Combine all scripts
    if script_contents:
        combined_script = '\n\n'.join(script_contents)

    else:
        # Return empty script if no tools are active
        combined_script = '// No tools are currently active for this website'
    
    # Set up the response with proper headers
    response = HttpResponse(combined_script, content_type='application/javascript')
    response['Cache-Control'] = 'max-age=3600'
    response['Access-Control-Allow-Origin'] = '*'
    response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
    
    return response
