"""
Django settings for AbunDRFBackend project.

Generated by 'django-admin startproject' using Django 4.1.7.

For more information on this file, see
https://docs.djangoproject.com/en/4.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.1/ref/settings/
"""
import logging
import os
import sys
from datetime import timedelta
from pathlib import Path

import colorlog
import openai
import stripe
import redis
from dotenv import load_dotenv

# from kubernetes import client as kube_client, config as kube_config
import kubernetes.client
import kubernetes.config
from kubernetes.client import V1NamespaceList

import sentry_sdk
from sentry_sdk.integrations.django import DjangoIntegration
from sentry_sdk.integrations.logging import LoggingIntegration

from langchain.globals import set_llm_cache
from langchain_community.cache import RedisCache

# set the environment variables
load_dotenv()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.environ['SECRET_KEY']

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = '1' == os.environ['DEBUG']

# Admin Secret
ADMIN_SECRET = os.environ['ADMIN_SECRET']

# --------------------- Logger Setup ---------------------
if os.environ['USE_SENTRY'] == "1":
    print("[*] Sentry logging: ON")
    sentry_logging = LoggingIntegration(
        level=logging.ERROR,           # Capture this and above levels as breadcrumbs.
        event_level=logging.CRITICAL   # Send these as events.
    )

    sentry_sdk.init(
        dsn="https://<EMAIL>/4506235188412416",
        traces_sample_rate=1.0,
        profiles_sample_rate=0.5,
        integrations=[
            sentry_logging,
            DjangoIntegration(),
        ],
        release='abundrf@v3.84.0'
    )
else:
    print("[*] Sentry logging: OFF")

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'colored': {
            '()': 'colorlog.ColoredFormatter',
            'format': '{log_color} {name} [{levelname}] - {message}',
            'style': '{',
            'log_colors': {
                'DEBUG': 'cyan',
                'INFO': 'green',
                'WARNING': 'yellow',
                'ERROR': 'red',
                'CRITICAL': 'red,bg_white',
            },
        }
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'colored',
        },
        'memory': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'stream': sys.stdout,
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'WARNING',
    },
    'loggers': {
        'abun': {
            'handlers': ['console'],
            'level': os.environ['LOG_LEVEL'],
            'propagate': False,
        },
        'abun_memory_usage': {
            'handlers': ['memory'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
# --------------------------------------------------------

ALLOWED_HOSTS = os.environ['ALLOWED_HOSTS'].split(',')
if os.environ['CORS_ALLOWED_ORIGINS'] == "*":
    print("[*] CORS: All origins allowed")
    CORS_ALLOWED_ORIGINS = []
    CORS_ALLOW_ALL_ORIGINS = True
else:
    CORS_ALLOWED_ORIGINS = os.environ['CORS_ALLOWED_ORIGINS'].split(',')

try:
    # Define Sentinel nodes
    if os.environ.get('REDIS_SENTINELS_PORTS'):
        sentinel_nodes = os.environ['REDIS_SENTINELS_PORTS'].split(',')
        sentinels = [
            (os.environ['REDIS_HOST'], int(sentinel_nodes[0])),
            (os.environ['REDIS_HOST'], int(sentinel_nodes[1])),
            (os.environ['REDIS_HOST'], int(sentinel_nodes[2])),
        ]

        # Set up Redis connection
        sentinel = redis.Sentinel(sentinels, socket_timeout=60, password=os.environ['REDIS_PASSWORD'])
        redis_client = sentinel.master_for('abun-redis-master', password=os.environ['REDIS_PASSWORD'],
                                           db=os.environ['REDIS_TASK_DATA_DB'])

    else:
        redis_client = redis.Redis(
            host=os.environ['REDIS_HOST'],
            port=os.environ['REDIS_PORT'],
            db=os.environ['REDIS_TASK_DATA_DB'],
            password=os.environ.get('REDIS_PASSWORD')
        )

    # Initialize RedisCache
    redis_cache = RedisCache(redis_=redis_client, ttl=None)

    # Set up redis cache for LangChain
    set_llm_cache(redis_cache)

    print("[*] LangChain cache configured successfully")

except Exception as e:
    print(f"[x] Failed to configure LangChain cache: {e}")

# Application definition
INSTALLED_APPS = [
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'mainapp.apps.MainappConfig',
    'rest_framework',
    'storages',
    'rest_framework_simplejwt.token_blacklist',
    'corsheaders',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'AbunDRFBackend.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    )
}

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=15),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=5),
    'ROTATE_REFRESH_TOKENS': True,
    'USER_ID_FIELD': 'email',
    'SIGNING_KEY': os.environ['JWT_SIGNING_KEY'],
}

WSGI_APPLICATION = 'AbunDRFBackend.wsgi.application'

# Database
# https://docs.djangoproject.com/en/4.1/ref/settings/#databases
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.environ['DB_NAME'],
        'USER': os.environ['DB_USER'],
        'PASSWORD': os.environ['DB_PASSWORD'],
        'HOST': os.environ['DB_HOST'],
        'PORT': os.environ['DB_PORT'],
        'CONN_MAX_AGE': 0,
    }
}

CACHES = {
    "default": {
        "BACKEND": "django.core.cache.backends.db.DatabaseCache",
        "LOCATION": "abun_mainapp_cache_table",
    }
}

# Authentication
AUTH_USER_MODEL = 'mainapp.User'

# Password validation
# https://docs.djangoproject.com/en/4.1/ref/settings/#auth-password-validators
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/4.1/topics/i18n/
LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'UTC'
USE_I18N = True
USE_TZ = True

# Default primary key field type
# https://docs.djangoproject.com/en/4.1/ref/settings/#default-auto-field
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Kubernetes
K8_JOB_RETRIES = 6
if os.environ.get('RUNNING_ON_K8', False):
    kubernetes.config.load_incluster_config()
else:
    k8_config_file_path = os.environ['K8_CONFIG_PATH']
    kubernetes.config.load_kube_config(k8_config_file_path)

# Django settings
DATA_UPLOAD_MAX_MEMORY_SIZE = 5000000  # 5MB
FILE_UPLOAD_MAX_MEMORY_SIZE = 5000000  # 5MB

# Open AI Settings
openai.api_key = os.environ['OPENAI_API_KEY']
MINI_AI_TOOL_OPENAI_API_KEY = os.environ['MINI_AI_TOOL_OPENAI_API_KEY']

# Redis Settings
REDIS_HOST = os.environ['REDIS_HOST']
REDIS_PORT = os.environ['REDIS_PORT']
REDIS_PASSWORD = os.environ.get('REDIS_PASSWORD')
REDIS_TASK_DATA_DB = os.environ['REDIS_TASK_DATA_DB']
REDIS_STRIPE_DB = os.environ['REDIS_TASK_DATA_DB']
REDIS_MINI_AI_TOOL_DB = os.environ['REDIS_MINI_AI_TOOL_DB']
REDIS_CONTENT_PLAN_EXPIRY = int(os.environ['REDIS_CONTENT_PLAN_EXPIRY'])
REDIS_ART_GEN_EXPIRY = int(os.environ['REDIS_ART_GEN_EXPIRY'])
REDIS_MINI_AI_TOOL_EXPIRY = os.environ['REDIS_MINI_AI_TOOL_EXPIRY']
REDIS_GSC_INSIGHTS_DB = os.environ['REDIS_GSC_INSIGHTS_DB']
REDIS_GSC_INSIGHTS_EXPIRY = os.environ['REDIS_GSC_INSIGHTS_EXPIRY']


# AWS Settings
AWS_ACCESS_KEY_ID = os.environ['AWS_ACCESS_KEY_ID']
AWS_SECRET_ACCESS_KEY = os.environ['AWS_SECRET_ACCESS_KEY']

# Webflow Integration Settings
# WEBFLOW_CLIENT_ID: str = os.environ['WEBFLOW_CLIENT_ID']
# WEBFLOW_CLIENT_SECRET: str = os.environ['WEBFLOW_CLIENT_SECRET']
# WEBFLOW_SCOPE: str = " ".join(os.environ['WEBFLOW_SCOPE'].split(','))

# Stripe Settings
stripe.api_key = os.environ['STRIPE_API_KEY']
STRIPE_ENDPOINT_SECRET = os.environ['STRIPE_ENDPOINT_SECRET']
STRIPE_CUSTOMER_PORTAL_LINK = os.environ['STRIPE_CUSTOMER_PORTAL_LINK']
STRIPE_CUSTOMER_PORTAL_RETURN_URL = os.environ['STRIPE_CUSTOMER_PORTAL_RETURN_URL']

# Reset Password
RESET_PASSWORD_LINK_DOMAIN = os.environ['RESET_PASSWORD_LINK_DOMAIN']
RESET_PASSWORD_ENCRYPTION_KEY: bytes = os.environ['RESET_PASSWORD_ENCRYPTION_KEY'].encode('utf-8')
EMAIL_VERIFICATION_ENCRYPTION_KEY: bytes = os.environ['EMAIL_VERIFICATION_ENCRYPTION_KEY'].encode('utf-8')
RESET_PASSWORD_EXPIRY_HOURS: int = int(os.environ['RESET_PASSWORD_EXPIRY_HOURS'])

# Abun Email IDs
ABUN_NOTIFICATION_EMAIL = "<EMAIL>"
JD_EMAIL = "<EMAIL>"
AMIN_EMAIL = "<EMAIL>"
ADIL_EMAIL = "<EMAIL>"
ABUN_INFO_EMAIL = "<EMAIL>"


# Wordpress Integration Settings
WP_RETURN_URL_DOMAIN: str = os.environ['WP_RETURN_URL_DOMAIN']
WP_DOMAIN_ENCRYPTION_KEY: bytes = os.environ['WP_DOMAIN_ENCRYPTION_KEY'].encode('utf-8')


# Cloudflare R2 Settings (R2 supports S3 api)
DEFAULT_FILE_STORAGE = 'mainapp.storage_backends.PublicMediaStorage'
AWS_STORAGE_BUCKET_NAME = os.environ['CLOUDFLARE_R2_BUCKET_NAME']
AWS_S3_ENDPOINT_URL = f"https://{os.environ['CLOUDFLARE_R2_DOMAIN']}"
AWS_S3_ACCESS_KEY_ID = os.environ['CLOUDFLARE_R2_ACCESS_KEY']
AWS_S3_SECRET_ACCESS_KEY = os.environ['CLOUDFLARE_R2_SECRET_KEY']
AWS_S3_SIGNATURE_VERSION = 's3v4'
AWS_S3_CUSTOM_DOMAIN = f"{os.environ['CLOUDFLARE_R2_DOMAIN']}/{AWS_STORAGE_BUCKET_NAME}"
AWS_DEFAULT_ACL = 'public-read'


# Celery Settings
CELERY_TIMEZONE = os.environ['CELERY_TIMEZONE']
CELERY_BROKER_URL = os.environ['CELERY_BROKER_URL']
CELERY_RESULT_BACKEND = os.environ['CELERY_RESULT_BACKEND']
CELERY_WORKER_CONCURRENCY = os.environ['CELERY_WORKER_CONCURRENCY']
CELERY_WORKER_MAX_MEMORY_PER_CHILD = os.environ['CELERY_WORKER_MAX_MEMORY_PER_CHILD']
CELERY_BROKER_CONNECTION_RETRY_ON_STARTUP = True
CELERY_WORKER_CANCEL_LONG_RUNNING_TASKS_ON_CONNECTION_LOSS = True

# Admin Domain (used in failed article email notification)
ADMIN_DOMAIN = os.environ['ADMIN_DOMAIN']

# Shopify API Version
SHOPIFY_API_VERSION = os.environ["SHOPIFY_API_VERSION"]

# AppSumo (Oauth Settings)
APPSUMO_CLIENT_ID = os.environ['APPSUMO_CLIENT_ID']
APPSUMO_CLIENT_SECRET = os.environ['APPSUMO_CLIENT_SECRET']
APPSUMO_PRIVATE_KEY = os.environ['APPSUMO_PRIVATE_KEY']

# Website Scanning
WEBSITE_SCANNING_MAX_WORKER = 2  # DEBUG and 2 or 5

# FLY.io API HOST
FLY_API_HOST = "https://api.machines.dev/v1"

# FLY.io configuration (For Article Generation)
FLY_ARTICLE_GEN_DEPLOY_TOKEN = os.environ["FLY_ARTICLE_GEN_DEPLOY_TOKEN"]
FLY_ARTICLE_GEN_APP_NAME = os.environ["FLY_ARTICLE_GEN_APP_NAME"]
FLY_ARTICLE_GEN_IMAGE_URL = f"registry.fly.io/{FLY_ARTICLE_GEN_APP_NAME}:{os.environ['K8_IMAGE_TAG']}"

# FLY.io configuration (For Website Scanning)
FLY_WEBSITE_SCANNING_DEPLOY_TOKEN = os.environ["FLY_WEBSITE_SCANNING_DEPLOY_TOKEN"]
FLY_WEBSITE_SCANNING_APP_NAME = os.environ["FLY_WEBSITE_SCANNING_APP_NAME"]
FLY_WEBSITE_SCANNING_GEN_IMAGE_URL = f"registry.fly.io/{FLY_WEBSITE_SCANNING_APP_NAME}:{os.environ['K8_IMAGE_TAG']}"

# FLY.io configuration (For Competitor Finder v2)
FLY_COMPETITOR_FINDER_DEPLOY_TOKEN = os.environ["FLY_COMPETITOR_FINDER_DEPLOY_TOKEN"]
FLY_COMPETITOR_FINDER_APP_NAME = os.environ["FLY_COMPETITOR_FINDER_APP_NAME"]
FLY_COMPETITOR_FINDER_GEN_IMAGE_URL = f"registry.fly.io/{FLY_COMPETITOR_FINDER_APP_NAME}:{os.environ['K8_IMAGE_TAG']}"

# FLY.io configuration (For Article Internal Linking)
FLY_ARTICLE_INTERNAL_LINK_DEPLOY_TOKEN = os.environ["FLY_ARTICLE_INTERNAL_LINK_DEPLOY_TOKEN"]
FLY_ARTICLE_INTERNAL_LINK_APP_NAME = os.environ["FLY_ARTICLE_INTERNAL_LINK_APP_NAME"]
FLY_ARTICLE_INTERNAL_LINK_IMAGE_URL = f"registry.fly.io/{FLY_ARTICLE_INTERNAL_LINK_APP_NAME}:{os.environ['K8_IMAGE_TAG']}"

# FLY.io configuration (For AI Calculator Generation)
FLY_AI_CALCULATOR_DEPLOY_TOKEN = os.environ["FLY_AI_CALCULATOR_DEPLOY_TOKEN"]
FLY_AI_CALCULATOR_APP_NAME = os.environ["FLY_AI_CALCULATOR_APP_NAME"]
FLY_AI_CALCULATOR_IMAGE_URL = f"registry.fly.io/{FLY_AI_CALCULATOR_APP_NAME}:{os.environ['K8_IMAGE_TAG']}"

# FLY.io configuration (For Stats Generation)
FLY_STATS_GENERATION_DEPLOY_TOKEN = os.environ["FLY_STATS_GENERATION_DEPLOY_TOKEN"]
FLY_STATS_GENERATION_APP_NAME = os.environ["FLY_STATS_GENERATION_APP_NAME"]
FLY_STATS_GENERATION_IMAGE_URL = f"registry.fly.io/{FLY_STATS_GENERATION_APP_NAME}:{os.environ['K8_IMAGE_TAG']}"

# FLY.io configuration (For Comparison Page Generation)
FLY_COMPARISON_GENERATION_DEPLOY_TOKEN = os.environ["FLY_COMPARISON_GENERATION_DEPLOY_TOKEN"]
FLY_COMPARISON_GENERATION_APP_NAME = os.environ["FLY_COMPARISON_GENERATION_APP_NAME"]
FLY_COMPARISON_GENERATION_IMAGE_URL = f"registry.fly.io/{FLY_COMPARISON_GENERATION_APP_NAME}:{os.environ['K8_IMAGE_TAG']}"

# Pexels(https://www.pexels.com/api/key/) api key to fetch image for featured image
PEXELS_API_KEY = os.environ['PEXELS_API_KEY']

# GHL Integration
GHL_CLIENT_ID = os.environ["GHL_CLIENT_ID"]
GHL_CLIENT_SECRET = os.environ["GHL_CLIENT_SECRET"]

# Reddit API Key
REDDIT_SECRET_KEY = os.environ["REDDIT_SECRET_KEY"]
REDDIT_CLIENT_KEY = os.environ["REDDIT_CLIENT_KEY"]

# YouTube API Key
YOUTUBE_API_KEY = os.environ["YOUTUBE_API_KEY"]
