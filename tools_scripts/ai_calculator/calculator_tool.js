(function (w, d, s, o) {
    // Unique ID for the calculator
    var calculatorId = o.calculatorId || 'default';
    var htmlContent = o.htmlContent || '';

    // Look for the target div with data-ai-calculator-id attribute matching calculatorId
    var targetDiv = d.querySelector('[data-ai-calculator-id="' + calculatorId + '"]');

    if (!targetDiv) {
        console.error('Abun AI Calculator: Target div with data-ai-calculator-id="' + calculatorId + '" not found. Please add <div data-ai-calculator-id="' + calculatorId + '"></div> to your page.');
        return;
    }

    // Validate that HTML content is provided
    if (!htmlContent || htmlContent.trim() === '') {
        console.error('Abun AI Calculator: No HTML content provided for calculator.');
        targetDiv.innerHTML = '<div style="display: flex; justify-content: center; align-items: center; height: 400px; font-family: Arial, sans-serif; color: #e74c3c;">Error: No calculator content provided.</div>';
        return;
    }

    // Create the calculator container
    var calculatorContainer = d.createElement('div');
    calculatorContainer.id = 'calculator-widget-' + calculatorId;
    calculatorContainer.style.width = '100%';
    calculatorContainer.style.height = 'auto';
    calculatorContainer.style.minHeight = '400px'; // Ensure minimum height
    calculatorContainer.style.backgroundColor = '#ffffff';
    calculatorContainer.style.borderRadius = '12px';
    calculatorContainer.style.boxShadow = '0 4px 15px rgba(0,0,0,0.1)';
    calculatorContainer.style.overflow = 'hidden';
    calculatorContainer.style.border = '1px solid #ddd';
    calculatorContainer.style.position = 'relative';

    // Clear any existing content in the target div and append calculator
    targetDiv.innerHTML = '';
    targetDiv.appendChild(calculatorContainer);

    // Show loading state briefly
    calculatorContainer.innerHTML = '<div style="display: flex; justify-content: center; align-items: center; height: 400px; font-family: Arial, sans-serif; color: #666;">Loading calculator...</div>';

    // Function to inject HTML content and execute scripts
    function injectCalculatorContent(content) {
        try {
            // Inject the calculator HTML directly into the container
            calculatorContainer.innerHTML = content;

            // Execute any scripts in the injected HTML
            var scripts = calculatorContainer.querySelectorAll('script');
            for (var i = 0; i < scripts.length; i++) {
                var script = scripts[i];
                var newScript = d.createElement('script');

                if (script.src) {
                    newScript.src = script.src;
                } else {
                    newScript.textContent = script.textContent;
                }

                // Copy any attributes
                for (var j = 0; j < script.attributes.length; j++) {
                    var attr = script.attributes[j];
                    if (attr.name !== 'src') {
                        newScript.setAttribute(attr.name, attr.value);
                    }
                }

                // Replace the old script with the new one to ensure execution
                script.parentNode.replaceChild(newScript, script);
            }
        } catch (error) {
            console.error('Abun AI Calculator: Error injecting calculator content:', error);
            calculatorContainer.innerHTML = '<div style="display: flex; justify-content: center; align-items: center; height: 400px; font-family: Arial, sans-serif; color: #e74c3c;">Error loading calculator content.</div>';
        }
    }

    // Inject the HTML content after a brief delay to show loading state
    setTimeout(function() {
        injectCalculatorContent(htmlContent);
    }, 100);

    // Expose API for external control
    w.CustomCalculator = w.CustomCalculator || {};
    w.CustomCalculator[calculatorId] = {
        show: function () {
            calculatorContainer.style.display = 'block';
        },
        hide: function () {
            calculatorContainer.style.display = 'none';
        },
        reload: function () {
            // Reload the calculator with the same HTML content
            calculatorContainer.innerHTML = '<div style="display: flex; justify-content: center; align-items: center; height: 400px; font-family: Arial, sans-serif; color: #666;">Reloading calculator...</div>';
            
            setTimeout(function() {
                injectCalculatorContent(htmlContent);
            }, 100);
        },
        updateContent: function (newHtmlContent) {
            // Allow updating the calculator with new HTML content
            if (newHtmlContent && newHtmlContent.trim() !== '') {
                htmlContent = newHtmlContent;
                calculatorContainer.innerHTML = '<div style="display: flex; justify-content: center; align-items: center; height: 400px; font-family: Arial, sans-serif; color: #666;">Updating calculator...</div>';
                
                setTimeout(function() {
                    injectCalculatorContent(htmlContent);
                }, 100);
            } else {
                console.error('Abun AI Calculator: Invalid HTML content provided for update.');
            }
        },
        getContainer: function () {
            return calculatorContainer;
        },
        getTargetDiv: function () {
            return targetDiv;
        }
    };

})(window, document, 'script', {
    calculatorId: `CALCULATOR_ID`,
    htmlContent: 'HTML_CONTENT'
});